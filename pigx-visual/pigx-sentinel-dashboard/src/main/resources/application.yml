server:
  port: 5020
  servlet:
    encoding:
      force: true
spring:
  application:
    name: @artifactId@
  cloud:
    nacos:
      username: @nacos.username@
      password: @nacos.password@
      discovery:
        server-addr: 127.0.0.1:8848

management:
  endpoints:
    web:
      exposure:
        include: '*'
  endpoint:
    health:
      show-details: ALWAYS
logging:
  level:
    org:
      springframework:
        web: info
  file:
    name: ${user.home}/logs/csp/sentinel-dashboard.log
  pattern:
    file: '%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n'

auth:
  username: sentinel
  password: sentinel
  filter:
    exclude-urls: /,/auth/login,/auth/logout,/registry/machine,/version,/actuator/**,/details
    exclude-url-suffixes: htm,html,js,css,map,ico,ttf,woff,png

sentinel:
  dashboard:
    version: @project.version@
