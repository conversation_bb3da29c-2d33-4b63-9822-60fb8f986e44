<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pig4cloud.pigx.portal.mapper.PortalFoodMapper">

    <resultMap id="BaseResultMap" type="com.pig4cloud.pigx.portal.entity.PortalFood">
            <id property="id" column="id" />
            <result property="foodName" column="food_name" />
            <result property="coverId" column="cover_id" />
            <result property="contentSummary" column="content_summary" />
            <result property="content" column="content" />
            <result property="restaurantOne" column="restaurant_one" />
            <result property="linkUrlOne" column="link_url_one" />
            <result property="codeIdOne" column="code_id_one" />
            <result property="restaurantTwo" column="restaurant_two" />
            <result property="linkUrlTwo" column="link_url_two" />
            <result property="codeIdTwo" column="code_id_two" />
            <result property="restaurantThree" column="restaurant_three" />
            <result property="linkUrlThree" column="link_url_three" />
            <result property="codeIdThree" column="code_id_three" />
            <result property="publishStatus" column="publish_status" />
            <result property="remark" column="remark" />
            <result property="creator" column="creator" />
            <result property="modifier" column="modifier" />
            <result property="gmtCreate" column="gmt_create" />
            <result property="gmtModified" column="gmt_modified" />
    </resultMap>

    <sql id="Base_Column_List">
        id,food_name,cover_id,content_summary,content,restaurant_one,link_url_one,
        code_id_one,restaurant_two,link_url_two,code_id_two,restaurant_three,
        link_url_three,code_id_three,publish_status,remark,creator,
        modifier,gmt_create,gmt_modified
    </sql>

    <select id="selectPageList" resultType="com.pig4cloud.pigx.portal.vo.PortalFoodListVO">
        select
            A.id,
            A.food_name as foodName,
            A.cover_id as coverId,
            A.content_summary as contentSummary,
            A.content as content,
            A.restaurant_one as restaurantOne,
            A.link_url_one as linkUrlOne,
            A.code_id_one as codeIdOne,
            A.restaurant_two as restaurantTwo,
            A.link_url_two as linkUrlTwo,
            A.code_id_two as codeIdTwo,
            A.restaurant_three as restaurantThree,
            A.link_url_three as linkUrlThree,
            A.code_id_three as codeIdThree,
            A.publish_status as publishStatus,
            A.sort_index as sortIndex,
            A.remark as remark,
            A.creator as creator,
            A.modifier as modifier,
            A.gmt_create as gmtCreate,
            A.gmt_modified as gmtModified,
            B.username as creatorName
        from portal_food A left join jiuhua.sys_user B on A.creator = B.user_id
        where 1 = 1
        <if test="dto.foodName != null and dto.foodName != ''">
            and food_name like concat('%',#{dto.foodName},'%')
        </if>
        <if test="dto.publishStatus != null">
            and publish_status = #{dto.publishStatus}
        </if>
    </select>

    <select id="foodList" resultType="com.pig4cloud.pigx.portal.vo.PortalFoodListVO">
        SELECT
            <include refid="Base_Column_List"/>
        FROM portal_food
        WHERE publish_status = 1
        ORDER BY sort_index ASC, gmt_create DESC
        LIMIT 6
    </select>

    <update  id="updatePublishStatus">
        update portal_food
        set publish_status = #{publishStatus}
        where id in
        <foreach item="item" collection="ids" separator="," open="(" close=")" index="">
            #{item}
        </foreach>
    </update>
</mapper>
