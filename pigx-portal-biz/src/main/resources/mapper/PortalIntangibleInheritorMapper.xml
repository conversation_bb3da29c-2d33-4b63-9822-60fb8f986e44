<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pig4cloud.pigx.portal.mapper.PortalIntangibleInheritorMapper">

    <resultMap id="BaseResultMap" type="com.pig4cloud.pigx.portal.entity.PortalIntangibleInheritor">
            <id property="id" column="id" />
            <result property="inheritorName" column="inheritor_name" />
            <result property="inheritorLevel" column="inheritor_level" />
            <result property="inheritProject" column="inherit_project" />
            <result property="contribute" column="contribute" />
            <result property="contentSummary" column="content_summary" />
            <result property="content" column="content" />
            <result property="coverId" column="cover_id" />
            <result property="publishStatus" column="publish_status" />
            <result property="remark" column="remark" />
            <result property="creator" column="creator" />
            <result property="modifier" column="modifier" />
            <result property="gmtCreate" column="gmt_create" />
            <result property="gmtModified" column="gmt_modified" />
    </resultMap>

    <sql id="Base_Column_List">
        id,inheritor_name,inheritor_level,inherit_project,contribute,content_summary,
        content,cover_id,publish_status,remark,creator,
        modifier,gmt_create,gmt_modified
    </sql>
</mapper>
