package com.pig4cloud.pigx.portal.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.pig4cloud.pigx.portal.dto.PortalIntangibleInheritorDTO;
import com.pig4cloud.pigx.portal.dto.PortalIntangibleInheritorListDTO;
import com.pig4cloud.pigx.portal.dto.PublishStatusDTO;
import com.pig4cloud.pigx.portal.entity.PortalIntangibleInheritor;
import com.baomidou.mybatisplus.extension.service.IService;
import com.pig4cloud.pigx.portal.vo.PortalIntangibleInheritorListVO;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【portal_intangible_inheritor(非遗传承人)】的数据库操作Service
* @createDate 2025-08-05 10:27:54
*/
public interface PortalIntangibleInheritorService extends IService<PortalIntangibleInheritor> {

    void add(PortalIntangibleInheritor portalIntangibleInheritor);

    void edit(PortalIntangibleInheritorDTO dto);

    void updatePublishStatus(PublishStatusDTO dto);

    void delete(List<Long> ids);

    PortalIntangibleInheritorListVO detail(Long id);

    IPage<PortalIntangibleInheritorListVO> pageList(IPage<PortalIntangibleInheritorListVO> pageParam, PortalIntangibleInheritorListDTO dto);

    List<PortalIntangibleInheritorListVO> inheritorList();

}
