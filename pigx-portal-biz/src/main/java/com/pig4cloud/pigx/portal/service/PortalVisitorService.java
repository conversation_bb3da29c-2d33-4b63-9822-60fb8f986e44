package com.pig4cloud.pigx.portal.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.pig4cloud.pigx.common.core.util.R;
import com.pig4cloud.pigx.portal.dto.SmsCodeDTO;
import com.pig4cloud.pigx.portal.dto.VisitorLoginDTO;
import com.pig4cloud.pigx.portal.dto.VisitorRegisterDTO;
import com.pig4cloud.pigx.portal.entity.PortalVisitor;
import com.pig4cloud.pigx.portal.vo.VisitorLoginVO;

/**
 * <AUTHOR>
 * @description 针对表【portal_visitor(游客用户)】的数据库操作Service
 * @createDate 2025-08-07
 */
public interface PortalVisitorService extends IService<PortalVisitor> {

    /**
     * 发送短信验证码
     * @param smsCodeDTO 短信验证码DTO
     * @return 发送结果
     */
    R<Boolean> sendSmsCode(SmsCodeDTO smsCodeDTO);

    /**
     * 游客注册
     * @param registerDTO 注册信息
     * @return 注册结果
     */
    R<Boolean> register(VisitorRegisterDTO registerDTO);

    /**
     * 游客登录
     * @param loginDTO 登录信息
     * @return 登录结果
     */
    R<VisitorLoginVO> login(VisitorLoginDTO loginDTO);

    /**
     * 根据用户名查询游客
     * @param username 用户名
     * @return 游客信息
     */
    PortalVisitor getByUsername(String username);

    /**
     * 根据手机号查询游客
     * @param phone 手机号
     * @return 游客信息
     */
    PortalVisitor getByPhone(String phone);
}
