package com.pig4cloud.pigx.portal.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.pig4cloud.pigx.portal.dto.PortalIntangibleProjectDTO;
import com.pig4cloud.pigx.portal.dto.PortalIntangibleProjectListDTO;
import com.pig4cloud.pigx.portal.dto.PublishStatusDTO;
import com.pig4cloud.pigx.portal.entity.PortalIntangibleProject;
import com.baomidou.mybatisplus.extension.service.IService;
import com.pig4cloud.pigx.portal.vo.PortalIntangibleProjectListVO;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【portal_intangible_project(非遗项目)】的数据库操作Service
* @createDate 2025-08-05 10:27:17
*/
public interface PortalIntangibleProjectService extends IService<PortalIntangibleProject> {

    void add(PortalIntangibleProject portalIntangibleProject);

    void edit(PortalIntangibleProjectDTO dto);

    void updatePublishStatus(PublishStatusDTO dto);

    void delete(List<Long> ids);

    PortalIntangibleProjectListVO detail(Long id);

    IPage<PortalIntangibleProjectListVO> pageList(IPage<PortalIntangibleProjectListVO> pageParam, PortalIntangibleProjectListDTO dto);

    List<PortalIntangibleProjectListVO> projectList();

}
