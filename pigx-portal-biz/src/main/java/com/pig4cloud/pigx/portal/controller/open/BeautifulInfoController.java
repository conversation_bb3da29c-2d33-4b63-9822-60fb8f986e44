package com.pig4cloud.pigx.portal.controller.open;

import cn.hutool.core.lang.tree.Tree;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.pig4cloud.pigx.admin.api.feign.RemoteDictTreeService;
import com.pig4cloud.pigx.common.core.constant.SecurityConstants;
import com.pig4cloud.pigx.common.core.util.R;
import com.pig4cloud.pigx.portal.dto.PortalBeautifulInfoListDTO;
import com.pig4cloud.pigx.portal.dto.PortalPhotographyListDTO;
import com.pig4cloud.pigx.portal.entity.PortalBeautifulInfo;
import com.pig4cloud.pigx.portal.service.PortalBeautifulInfoService;
import com.pig4cloud.pigx.portal.service.PortalPhotographyService;
import com.pig4cloud.pigx.portal.vo.PortalBeautifulInfoListVO;
import com.pig4cloud.pigx.portal.vo.PortalPhotographyListVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * 门户网站-秀美九华
 */
@Slf4j
@RestController
@RequestMapping("/open/beautify")
@RequiredArgsConstructor
public class BeautifulInfoController {

    @Resource
    private RemoteDictTreeService remoteDictTreeService;

    @Resource
    private PortalBeautifulInfoService portalBeautifulInfoService;

    @Resource
    private PortalPhotographyService portalPhotographyService;

    /**
     * 通过字典类型查找字典
     * @param treeType 类型
     * @return 同类型字典
     */
    @GetMapping("/treeType/{treeType}")
    public R<List<Tree<Long>>> getDictByTypeForInner(@PathVariable String treeType) {
        return R.ok(remoteDictTreeService.getDictByTypeForInner(treeType, SecurityConstants.FROM_IN).getData(),"");
    }

    /**
     * 秀美九华分页查询
     *
     * @param page 分页对象
     * @return
     */
    @GetMapping("/beautifulPage")
    public R<IPage<PortalBeautifulInfoListVO>> getPageList(@ParameterObject Page page,
                                                           @ParameterObject PortalBeautifulInfoListDTO dto) {
        return R.ok(portalBeautifulInfoService.pageList(page, dto));
    }

    /**
     * 九华风景
     *
     * @return
     */
    @GetMapping("/fjList")
    public R<List<PortalBeautifulInfoListVO>> getFjList() {
        List<Long> classifySmall = new ArrayList<>();
        classifySmall.add(1930137497934086146L);
        return R.ok(portalBeautifulInfoService.fjList(1930136341002436610L,classifySmall));
    }

    /**
     * 九华生态
     *
     * @return
     */
    @GetMapping("/stList")
    public R<List<PortalBeautifulInfoListVO>> stList() {
        List<Long> classifySmall = new ArrayList<>();
        classifySmall.add(1930137547498176514L);
        classifySmall.add(1930137601776664578L);
        classifySmall.add(1930137574119424001L);
        classifySmall.add(1930137633431076865L);
        return R.ok(portalBeautifulInfoService.fjList(1930136341002436610L,classifySmall));
    }


    /**
     * 走进九华查询详情
     *
     * @return
     */
    @GetMapping("/detail/{id}")
    public R<PortalBeautifulInfo> detail(@PathVariable Long id) {
        return R.ok(portalBeautifulInfoService.detail(id));
    }

    /**
     * 摄影欣赏分页查询
     *
     * @param page 分页对象
     * @return
     */
    @GetMapping("/photographyPage")
    public R<IPage<PortalPhotographyListVO>> getPageList(@ParameterObject Page page,
                                                         @ParameterObject PortalPhotographyListDTO dto) {
        dto.setPublishStatus(1);
        return R.ok(portalPhotographyService.pageList(page, dto));
    }



}
