package com.pig4cloud.pigx.portal.service.impl;

import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.digest.BCrypt;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.pig4cloud.pigx.common.core.constant.CacheConstants;
import com.pig4cloud.pigx.common.core.constant.SecurityConstants;
import com.pig4cloud.pigx.common.core.util.R;
import com.pig4cloud.pigx.portal.dto.SmsCodeDTO;
import com.pig4cloud.pigx.portal.dto.VisitorLoginDTO;
import com.pig4cloud.pigx.portal.dto.VisitorRegisterDTO;
import com.pig4cloud.pigx.portal.entity.PortalVisitor;
import com.pig4cloud.pigx.portal.mapper.PortalVisitorMapper;
import com.pig4cloud.pigx.portal.service.PortalVisitorService;
import com.pig4cloud.pigx.portal.vo.VisitorLoginVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.UUID;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @description 针对表【portal_visitor(游客用户)】的数据库操作Service实现
 * @createDate 2025-08-07
 */
@Slf4j
@Service
public class PortalVisitorServiceImpl extends ServiceImpl<PortalVisitorMapper, PortalVisitor>
    implements PortalVisitorService {

    @Resource
    private RedisTemplate<String, String> redisTemplate;

    private static final String SMS_CODE_PREFIX = "visitor:sms:";
    private static final String LOGIN_TOKEN_PREFIX = "visitor:token:";
    private static final int SMS_CODE_EXPIRE_TIME = 300; // 5分钟
    private static final int TOKEN_EXPIRE_TIME = 7200; // 2小时

    @Override
    public R<Boolean> sendSmsCode(SmsCodeDTO smsCodeDTO) {
        String phone = smsCodeDTO.getPhone();
        String type = smsCodeDTO.getType();
        
        // 检查验证码发送频率
        String cacheKey = SMS_CODE_PREFIX + type + ":" + phone;
        String existingCode = redisTemplate.opsForValue().get(cacheKey);
        
        if (StrUtil.isNotBlank(existingCode)) {
            log.info("手机号验证码未过期: {}", phone);
            return R.failed("验证码发送过于频繁，请稍后再试");
        }

        // 注册时检查手机号是否已存在
        if ("register".equals(type)) {
            PortalVisitor existingVisitor = getByPhone(phone);
            if (existingVisitor != null) {
                return R.failed("该手机号已注册");
            }
        }

        // 生成6位数字验证码
        String code = RandomUtil.randomNumbers(6);
        log.info("为手机号 {} 生成验证码: {}", phone, code);
        
        // 存储验证码到Redis，设置过期时间
        redisTemplate.opsForValue().set(cacheKey, code, SMS_CODE_EXPIRE_TIME, TimeUnit.SECONDS);
        
        // TODO: 这里应该调用短信服务发送验证码，目前仅记录日志
        log.info("短信验证码发送成功，手机号: {}, 验证码: {}", phone, code);
        
        return R.ok(true, "验证码发送成功");
    }

    @Override
    public R<Boolean> register(VisitorRegisterDTO registerDTO) {
        // 验证密码确认
        if (!registerDTO.getPassword().equals(registerDTO.getConfirmPassword())) {
            return R.failed("两次输入的密码不一致");
        }

        // 验证用户名是否已存在
        PortalVisitor existingUser = getByUsername(registerDTO.getUsername());
        if (existingUser != null) {
            return R.failed("用户名已存在");
        }

        // 验证手机号是否已存在
        PortalVisitor existingPhone = getByPhone(registerDTO.getPhone());
        if (existingPhone != null) {
            return R.failed("手机号已注册");
        }

        // 验证短信验证码
        String cacheKey = SMS_CODE_PREFIX + "register:" + registerDTO.getPhone();
        String cachedCode = redisTemplate.opsForValue().get(cacheKey);
        
        if (StrUtil.isBlank(cachedCode)) {
            return R.failed("验证码已过期，请重新获取");
        }
        
        if (!cachedCode.equals(registerDTO.getSmsCode())) {
            return R.failed("验证码错误");
        }

        // 创建新用户
        PortalVisitor visitor = new PortalVisitor();
        visitor.setUsername(registerDTO.getUsername());
        visitor.setPassword(BCrypt.hashpw(registerDTO.getPassword(), BCrypt.gensalt()));
        visitor.setPhone(registerDTO.getPhone());
        visitor.setNickname(StrUtil.isNotBlank(registerDTO.getNickname()) ? 
                           registerDTO.getNickname() : registerDTO.getUsername());
        visitor.setStatus(0); // 正常状态

        boolean saved = save(visitor);
        
        if (saved) {
            // 删除已使用的验证码
            redisTemplate.delete(cacheKey);
            return R.ok(true, "注册成功");
        } else {
            return R.failed("注册失败");
        }
    }

    @Override
    public R<VisitorLoginVO> login(VisitorLoginDTO loginDTO) {
        // 根据用户名查询用户
        PortalVisitor visitor = getByUsername(loginDTO.getUsername());
        
        if (visitor == null) {
            return R.failed("用户名或密码错误");
        }

        // 检查用户状态
        if (visitor.getStatus() != 0) {
            return R.failed("账户已被锁定");
        }

        // 验证密码
        if (!BCrypt.checkpw(loginDTO.getPassword(), visitor.getPassword())) {
            return R.failed("用户名或密码错误");
        }

        // 生成登录令牌
        String token = UUID.randomUUID().toString().replace("-", "");
        String tokenKey = LOGIN_TOKEN_PREFIX + token;
        
        // 将用户信息存储到Redis
        redisTemplate.opsForValue().set(tokenKey, visitor.getId().toString(), TOKEN_EXPIRE_TIME, TimeUnit.SECONDS);

        // 构建返回结果
        VisitorLoginVO loginVO = new VisitorLoginVO();
        BeanUtils.copyProperties(visitor, loginVO);
        loginVO.setToken(token);
        // 不返回密码
        loginVO.setPassword(null);

        return R.ok(loginVO, "登录成功");
    }

    @Override
    public PortalVisitor getByUsername(String username) {
        LambdaQueryWrapper<PortalVisitor> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(PortalVisitor::getUsername, username);
        return getOne(wrapper);
    }

    @Override
    public PortalVisitor getByPhone(String phone) {
        LambdaQueryWrapper<PortalVisitor> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(PortalVisitor::getPhone, phone);
        return getOne(wrapper);
    }
}
