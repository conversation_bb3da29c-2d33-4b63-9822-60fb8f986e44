package com.pig4cloud.pigx.portal.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;
import lombok.Data;

/**
 * 非遗传承人
 * @TableName portal_intangible_inheritor
 */
@TableName(value ="portal_intangible_inheritor")
@Data
public class PortalIntangibleInheritor {
    /**
     * 自增id
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 姓名
     */
    private String inheritorName;

    /**
     * 传承人级别
     */
    private Long inheritorLevel;

    /**
     * 传承项目
     */
    private String inheritProject;

    /**
     * 贡献及荣誉
     */
    private String contribute;

    /**
     * 摘要
     */
    private String contentSummary;

    /**
     * 传承故事介绍
     */
    private String content;

    /**
     * 封面图片
     */
    private Long coverId;

    /**
     * 发布状态：0未发布，1已发布
     */
    private Integer publishStatus;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建人
     */
    private Long creator;

    /**
     * 修改人
     */
    private Long modifier;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 修改时间
     */
    private Date gmtModified;
}