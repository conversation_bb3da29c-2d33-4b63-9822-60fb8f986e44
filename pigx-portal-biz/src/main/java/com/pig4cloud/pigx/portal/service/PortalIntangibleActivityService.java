package com.pig4cloud.pigx.portal.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.pig4cloud.pigx.portal.dto.PortalIntangibleActivityDTO;
import com.pig4cloud.pigx.portal.dto.PortalIntangibleActivityListDTO;
import com.pig4cloud.pigx.portal.dto.PublishStatusDTO;
import com.pig4cloud.pigx.portal.entity.PortalIntangibleActivity;
import com.baomidou.mybatisplus.extension.service.IService;
import com.pig4cloud.pigx.portal.vo.PortalIntangibleActivityListVO;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【portal_intangible_activity(非遗活动)】的数据库操作Service
* @createDate 2025-08-05 10:28:39
*/
public interface PortalIntangibleActivityService extends IService<PortalIntangibleActivity> {

    void add(PortalIntangibleActivity portalIntangibleActivity);

    void edit(PortalIntangibleActivityDTO dto);

    void updatePublishStatus(PublishStatusDTO dto);

    void delete(List<Long> ids);

    PortalIntangibleActivityListVO detail(Long id);

    IPage<PortalIntangibleActivityListVO> pageList(IPage<PortalIntangibleActivityListVO> pageParam, PortalIntangibleActivityListDTO dto);

    List<PortalIntangibleActivityListVO> getActivityList(String startDay, String endDay);
}
