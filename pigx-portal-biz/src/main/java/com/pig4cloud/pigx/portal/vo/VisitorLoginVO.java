package com.pig4cloud.pigx.portal.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 游客登录响应VO
 */
@Data
@Schema(description = "游客登录响应VO")
public class VisitorLoginVO {

    /**
     * 用户ID
     */
    @Schema(description = "用户ID")
    private Long id;

    /**
     * 用户名
     */
    @Schema(description = "用户名")
    private String username;

    /**
     * 手机号
     */
    @Schema(description = "手机号")
    private String phone;

    /**
     * 昵称
     */
    @Schema(description = "昵称")
    private String nickname;

    /**
     * 头像
     */
    @Schema(description = "头像")
    private String avatar;

    /**
     * 访问令牌
     */
    @Schema(description = "访问令牌")
    private String token;
}
