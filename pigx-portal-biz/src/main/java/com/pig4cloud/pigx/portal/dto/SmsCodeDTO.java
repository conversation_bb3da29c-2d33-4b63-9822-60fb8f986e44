package com.pig4cloud.pigx.portal.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;

/**
 * 短信验证码DTO
 */
@Data
@Schema(description = "短信验证码DTO")
public class SmsCodeDTO {

    /**
     * 手机号
     */
    @NotBlank(message = "手机号不能为空")
    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "手机号格式不正确")
    @Schema(description = "手机号")
    private String phone;

    /**
     * 验证码类型：register-注册，login-登录
     */
    @NotBlank(message = "验证码类型不能为空")
    @Schema(description = "验证码类型：register-注册，login-登录")
    private String type;
}
