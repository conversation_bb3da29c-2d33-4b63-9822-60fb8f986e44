package com.pig4cloud.pigx.portal.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;
import lombok.Data;

/**
 * 非遗活动
 * @TableName portal_intangible_activity
 */
@TableName(value ="portal_intangible_activity")
@Data
public class PortalIntangibleActivity {
    /**
     * 自增id
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 活动名称
     */
    private String activityName;

    /**
     * 活动时间
     */
    private Date activityTime;

    /**
     * 活动地点
     */
    private String address;

    /**
     * 关键词
     */
    private String keyword;

    /**
     * 摘要
     */
    private String contentSummary;

    /**
     * 活动介绍
     */
    private String content;

    /**
     * 封面图片
     */
    private Long coverId;

    /**
     * 发布状态：0未发布，1已发布
     */
    private Integer publishStatus;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建人
     */
    private Long creator;

    /**
     * 修改人
     */
    private Long modifier;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 修改时间
     */
    private Date gmtModified;
}