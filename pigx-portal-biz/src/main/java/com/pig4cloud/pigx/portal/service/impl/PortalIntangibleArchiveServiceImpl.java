package com.pig4cloud.pigx.portal.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.pig4cloud.pigx.common.security.service.PigxUser;
import com.pig4cloud.pigx.common.security.util.SecurityUtils;
import com.pig4cloud.pigx.portal.dto.PortalIntangibleArchiveDTO;
import com.pig4cloud.pigx.portal.dto.PortalIntangibleArchiveListDTO;
import com.pig4cloud.pigx.portal.dto.PublishStatusDTO;
import com.pig4cloud.pigx.portal.entity.PortalDocumentInfo;
import com.pig4cloud.pigx.portal.entity.PortalIntangibleArchive;
import com.pig4cloud.pigx.portal.service.PortalDocumentInfoService;
import com.pig4cloud.pigx.portal.service.PortalIntangibleArchiveService;
import com.pig4cloud.pigx.portal.mapper.PortalIntangibleArchiveMapper;
import com.pig4cloud.pigx.portal.util.MinioUtils;
import com.pig4cloud.pigx.portal.vo.PortalIntangibleArchiveListVO;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
* <AUTHOR>
* @description 针对表【portal_intangible_archive(非遗档案)】的数据库操作Service实现
* @createDate 2025-08-05 10:28:11
*/
@Service
public class PortalIntangibleArchiveServiceImpl extends ServiceImpl<PortalIntangibleArchiveMapper, PortalIntangibleArchive>
    implements PortalIntangibleArchiveService{

    @Resource
    private PortalDocumentInfoService portalDocumentInfoService;

    @Override
    public void add(PortalIntangibleArchive portalIntangibleArchive){
        PigxUser user = SecurityUtils.getUser();
        portalIntangibleArchive.setCreator(user.getId());
        portalIntangibleArchive.setModifier(user.getId());
        baseMapper.insert(portalIntangibleArchive);
    }

    @Override
    public void edit(PortalIntangibleArchiveDTO dto){
        PortalIntangibleArchive portalIntangibleArchive = baseMapper.selectById(dto.getId());
        if(Objects.nonNull(portalIntangibleArchive)){
            BeanUtils.copyProperties(dto, portalIntangibleArchive);
            PigxUser user = SecurityUtils.getUser();
            portalIntangibleArchive.setModifier(user.getId());
            baseMapper.updateById(portalIntangibleArchive);
        }
    }

    @Override
    public void updatePublishStatus(PublishStatusDTO dto){
        if(CollectionUtil.isNotEmpty(dto.getIds())){
            baseMapper.updatePublishStatus(dto.getIds(), dto.getPublishStatus());
        }
    }

    @Override
    public void delete(List<Long> ids){
        baseMapper.deleteBatchIds(ids);
    }

    @Override
    public PortalIntangibleArchiveListVO detail(Long id){
        PortalIntangibleArchiveListVO detailVO = new PortalIntangibleArchiveListVO();
        PortalIntangibleArchive detail = baseMapper.selectById(id);
        BeanUtil.copyProperties(detail, detailVO);
        if(detail.getCoverId() != null){
            PortalDocumentInfo coverInfo = portalDocumentInfoService.getById(detail.getCoverId());
            if(coverInfo != null){
                detailVO.setCoverUrl(MinioUtils.getVisitUrl(coverInfo.getResPath()));
            }
        }
        return detailVO;
    }

    @Override
    public IPage<PortalIntangibleArchiveListVO> pageList(IPage<PortalIntangibleArchiveListVO> pageParam, PortalIntangibleArchiveListDTO dto){
        Page<PortalIntangibleArchiveListVO> page = new Page<>(pageParam.getCurrent(), pageParam.getSize());
        IPage<PortalIntangibleArchiveListVO> pageList = baseMapper.selectPageList(page, dto);
        buildPageRecords(pageList);
        return pageList;
    }

    private void buildPageRecords(IPage<PortalIntangibleArchiveListVO> page) {
        if (!page.getRecords().isEmpty()) {
            List<PortalIntangibleArchiveListVO> detailPage = page.getRecords();
            List<Long> coverIds = detailPage.stream().map(PortalIntangibleArchiveListVO::getCoverId).filter(Objects::nonNull).collect(Collectors.toList());
            if(CollectionUtil.isNotEmpty(coverIds)){
                List<PortalDocumentInfo> portalDocumentInfos = portalDocumentInfoService.getBaseMapper().selectBatchIds(coverIds);
                Map<Long,String> coverUrlMap = portalDocumentInfos.stream().collect(Collectors.toMap(PortalDocumentInfo::getId, PortalDocumentInfo::getResPath));
                // 查询当前分页中的关联的附件
                page.setRecords(detailPage.stream().peek(item -> {
                    if(item.getCoverId() != null && coverUrlMap.containsKey(item.getCoverId())){
                        item.setCoverUrl(MinioUtils.getVisitUrl(coverUrlMap.get(item.getCoverId())));
                    }
                }).collect(Collectors.toList()));
            }
        }
    }

    @Override
    public List<PortalIntangibleArchiveListVO> getArchiveList(){
        QueryWrapper<PortalIntangibleArchive> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(PortalIntangibleArchive::getPublishStatus, 1);
        queryWrapper.lambda().orderByDesc(PortalIntangibleArchive::getGmtCreate);
        List<PortalIntangibleArchive> detailPage = baseMapper.selectList(queryWrapper);
        return detailPage.stream().map(item -> {
            PortalIntangibleArchiveListVO detailVO = new PortalIntangibleArchiveListVO();
            BeanUtils.copyProperties(item, detailVO);
            if(item.getCoverId() != null){
                PortalDocumentInfo coverInfo = portalDocumentInfoService.getById(item.getCoverId());
                if(coverInfo != null){
                    detailVO.setCoverUrl(MinioUtils.getVisitUrl(coverInfo.getResPath()));
                }
            }
            return detailVO;
        }).collect(Collectors.toList());
    }

}




