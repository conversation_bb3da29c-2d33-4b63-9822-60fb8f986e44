package com.pig4cloud.pigx.portal.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.pig4cloud.pigx.portal.entity.PortalIntangibleArchive;
import com.pig4cloud.pigx.portal.service.PortalIntangibleArchiveService;
import com.pig4cloud.pigx.portal.mapper.PortalIntangibleArchiveMapper;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【portal_intangible_archive(非遗档案)】的数据库操作Service实现
* @createDate 2025-08-05 10:28:11
*/
@Service
public class PortalIntangibleArchiveServiceImpl extends ServiceImpl<PortalIntangibleArchiveMapper, PortalIntangibleArchive>
    implements PortalIntangibleArchiveService{

}




