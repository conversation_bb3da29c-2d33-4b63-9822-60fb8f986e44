package com.pig4cloud.pigx.portal.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;
import lombok.Data;

/**
 * 非遗项目
 * @TableName portal_intangible_project
 */
@TableName(value ="portal_intangible_project")
@Data
public class PortalIntangibleProject {
    /**
     * 自增id
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 项目名称
     */
    private String projectName;

    /**
     * 项目级别
     */
    private Long projectLevel;

    /**
     * 历史起源
     */
    private String historicalOrigin;

    /**
     * 项目地点
     */
    private String address;

    /**
     * 举办日期
     */
    private String eventDay;

    /**
     * 摘要
     */
    private String contentSummary;

    /**
     * 非遗项目介绍
     */
    private String content;

    /**
     * 封面图片
     */
    private Long coverId;

    /**
     * 发布状态：0未发布，1已发布
     */
    private Integer publishStatus;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建人
     */
    private Long creator;

    /**
     * 修改人
     */
    private Long modifier;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 修改时间
     */
    private Date gmtModified;
}