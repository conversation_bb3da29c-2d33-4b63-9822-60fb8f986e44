package com.pig4cloud.pigx.portal.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.pig4cloud.pigx.common.security.service.PigxUser;
import com.pig4cloud.pigx.common.security.util.SecurityUtils;
import com.pig4cloud.pigx.portal.dto.PortalIntangibleProjectDTO;
import com.pig4cloud.pigx.portal.dto.PortalIntangibleProjectListDTO;
import com.pig4cloud.pigx.portal.dto.PublishStatusDTO;
import com.pig4cloud.pigx.portal.entity.PortalDocumentInfo;
import com.pig4cloud.pigx.portal.entity.PortalIntangibleProject;
import com.pig4cloud.pigx.portal.service.PortalDocumentInfoService;
import com.pig4cloud.pigx.portal.service.PortalIntangibleProjectService;
import com.pig4cloud.pigx.portal.mapper.PortalIntangibleProjectMapper;
import com.pig4cloud.pigx.portal.util.MinioUtils;
import com.pig4cloud.pigx.portal.vo.PortalIntangibleProjectListVO;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
* <AUTHOR>
* @description 针对表【portal_intangible_project(非遗项目)】的数据库操作Service实现
* @createDate 2025-08-05 10:27:16
*/
@Service
public class PortalIntangibleProjectServiceImpl extends ServiceImpl<PortalIntangibleProjectMapper, PortalIntangibleProject>
    implements PortalIntangibleProjectService{

    @Resource
    private PortalDocumentInfoService portalDocumentInfoService;

    @Override
    public void add(PortalIntangibleProject portalIntangibleProject){
        PigxUser user = SecurityUtils.getUser();
        portalIntangibleProject.setCreator(user.getId());
        portalIntangibleProject.setModifier(user.getId());
        baseMapper.insert(portalIntangibleProject);
    }

    @Override
    public void edit(PortalIntangibleProjectDTO dto){
        PortalIntangibleProject portalIntangibleProject = baseMapper.selectById(dto.getId());
        if(Objects.nonNull(portalIntangibleProject)){
            BeanUtils.copyProperties(dto, portalIntangibleProject);
            PigxUser user = SecurityUtils.getUser();
            portalIntangibleProject.setModifier(user.getId());
            baseMapper.updateById(portalIntangibleProject);
        }
    }

    @Override
    public void updatePublishStatus(PublishStatusDTO dto){
        if(CollectionUtil.isNotEmpty(dto.getIds())){
            baseMapper.updatePublishStatus(dto.getIds(), dto.getPublishStatus());
        }
    }

    @Override
    public void delete(List<Long> ids){
        baseMapper.deleteBatchIds(ids);
    }

    @Override
    public PortalIntangibleProjectListVO detail(Long id){
        PortalIntangibleProjectListVO detailVO = new PortalIntangibleProjectListVO();
        PortalIntangibleProject detail = baseMapper.selectById(id);
        BeanUtil.copyProperties(detail, detailVO);
        if(detail.getCoverId() != null){
            PortalDocumentInfo coverInfo = portalDocumentInfoService.getById(detail.getCoverId());
            if(coverInfo != null){
                detailVO.setCoverUrl(MinioUtils.getVisitUrl(coverInfo.getResPath()));
            }
        }
        return detailVO;
    }

    @Override
    public IPage<PortalIntangibleProjectListVO> pageList(IPage<PortalIntangibleProjectListVO> pageParam, PortalIntangibleProjectListDTO dto){
        Page<PortalIntangibleProjectListVO> page = new Page<>(pageParam.getCurrent(), pageParam.getSize());
        IPage<PortalIntangibleProjectListVO> pageList = baseMapper.selectPageList(page, dto);
        buildPageRecords(pageList);
        return pageList;
    }

    @Override
    public List<PortalIntangibleProjectListVO> projectList(){
        QueryWrapper<PortalIntangibleProject> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(PortalIntangibleProject::getPublishStatus, 1);
        List<PortalIntangibleProject> detailPage = baseMapper.selectList(queryWrapper);
        return detailPage.stream().map(item -> {
            PortalIntangibleProjectListVO detailVO = new PortalIntangibleProjectListVO();
            BeanUtils.copyProperties(item, detailVO);
            if(item.getCoverId() != null){
                PortalDocumentInfo coverInfo = portalDocumentInfoService.getById(item.getCoverId());
                if(coverInfo != null){
                    detailVO.setCoverUrl(MinioUtils.getVisitUrl(coverInfo.getResPath()));
                }
            }
            return detailVO;
        }).collect(Collectors.toList());
    }

    private void buildPageRecords(IPage<PortalIntangibleProjectListVO> page) {
        if (!page.getRecords().isEmpty()) {
            List<PortalIntangibleProjectListVO> detailPage = page.getRecords();
            List<Long> coverIds = detailPage.stream().map(PortalIntangibleProjectListVO::getCoverId).filter(Objects::nonNull).collect(Collectors.toList());
            if(CollectionUtil.isNotEmpty(coverIds)){
                List<PortalDocumentInfo> portalDocumentInfos = portalDocumentInfoService.getBaseMapper().selectBatchIds(coverIds);
                Map<Long,String> coverUrlMap = portalDocumentInfos.stream().collect(Collectors.toMap(PortalDocumentInfo::getId, PortalDocumentInfo::getResPath));
                // 查询当前分页中的关联的附件
                page.setRecords(detailPage.stream().peek(item -> {
                    if(item.getCoverId() != null && coverUrlMap.containsKey(item.getCoverId())){
                        item.setCoverUrl(MinioUtils.getVisitUrl(coverUrlMap.get(item.getCoverId())));
                    }
                }).collect(Collectors.toList()));
            }
        }
    }

}




