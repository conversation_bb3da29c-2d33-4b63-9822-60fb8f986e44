package com.pig4cloud.pigx.portal.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.pig4cloud.pigx.portal.entity.PortalIntangibleProject;
import com.pig4cloud.pigx.portal.service.PortalIntangibleProjectService;
import com.pig4cloud.pigx.portal.mapper.PortalIntangibleProjectMapper;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【portal_intangible_project(非遗项目)】的数据库操作Service实现
* @createDate 2025-08-05 10:27:16
*/
@Service
public class PortalIntangibleProjectServiceImpl extends ServiceImpl<PortalIntangibleProjectMapper, PortalIntangibleProject>
    implements PortalIntangibleProjectService{

}




