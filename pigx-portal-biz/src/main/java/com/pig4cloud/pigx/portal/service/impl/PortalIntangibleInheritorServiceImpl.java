package com.pig4cloud.pigx.portal.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.pig4cloud.pigx.portal.entity.PortalIntangibleInheritor;
import com.pig4cloud.pigx.portal.service.PortalIntangibleInheritorService;
import com.pig4cloud.pigx.portal.mapper.PortalIntangibleInheritorMapper;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【portal_intangible_inheritor(非遗传承人)】的数据库操作Service实现
* @createDate 2025-08-05 10:27:54
*/
@Service
public class PortalIntangibleInheritorServiceImpl extends ServiceImpl<PortalIntangibleInheritorMapper, PortalIntangibleInheritor>
    implements PortalIntangibleInheritorService{

}




