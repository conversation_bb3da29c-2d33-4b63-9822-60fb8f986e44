package com.pig4cloud.pigx.portal.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.pig4cloud.pigx.common.security.service.PigxUser;
import com.pig4cloud.pigx.common.security.util.SecurityUtils;
import com.pig4cloud.pigx.portal.dto.PortalIntangibleInheritorDTO;
import com.pig4cloud.pigx.portal.dto.PortalIntangibleInheritorListDTO;
import com.pig4cloud.pigx.portal.dto.PublishStatusDTO;
import com.pig4cloud.pigx.portal.entity.PortalDocumentInfo;
import com.pig4cloud.pigx.portal.entity.PortalIntangibleInheritor;
import com.pig4cloud.pigx.portal.service.PortalDocumentInfoService;
import com.pig4cloud.pigx.portal.service.PortalIntangibleInheritorService;
import com.pig4cloud.pigx.portal.mapper.PortalIntangibleInheritorMapper;
import com.pig4cloud.pigx.portal.util.MinioUtils;
import com.pig4cloud.pigx.portal.vo.PortalIntangibleInheritorListVO;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
* <AUTHOR>
* @description 针对表【portal_intangible_inheritor(非遗传承人)】的数据库操作Service实现
* @createDate 2025-08-05 10:27:54
*/
@Service
public class PortalIntangibleInheritorServiceImpl extends ServiceImpl<PortalIntangibleInheritorMapper, PortalIntangibleInheritor>
    implements PortalIntangibleInheritorService{

    @Resource
    private PortalDocumentInfoService portalDocumentInfoService;

    @Override
    public void add(PortalIntangibleInheritor portalIntangibleInheritor){
        PigxUser user = SecurityUtils.getUser();
        portalIntangibleInheritor.setCreator(user.getId());
        portalIntangibleInheritor.setModifier(user.getId());
        baseMapper.insert(portalIntangibleInheritor);
    }

    @Override
    public void edit(PortalIntangibleInheritorDTO dto){
        PortalIntangibleInheritor portalIntangibleInheritor = baseMapper.selectById(dto.getId());
        if(Objects.nonNull(portalIntangibleInheritor)){
            BeanUtils.copyProperties(dto, portalIntangibleInheritor);
            PigxUser user = SecurityUtils.getUser();
            portalIntangibleInheritor.setModifier(user.getId());
            baseMapper.updateById(portalIntangibleInheritor);
        }
    }

    @Override
    public void updatePublishStatus(PublishStatusDTO dto){
        if(CollectionUtil.isNotEmpty(dto.getIds())){
            baseMapper.updatePublishStatus(dto.getIds(), dto.getPublishStatus());
        }
    }

    @Override
    public void delete(List<Long> ids){
        baseMapper.deleteBatchIds(ids);
    }

    @Override
    public PortalIntangibleInheritorListVO detail(Long id){
        PortalIntangibleInheritorListVO detailVO = new PortalIntangibleInheritorListVO();
        PortalIntangibleInheritor detail = baseMapper.selectById(id);
        BeanUtil.copyProperties(detail, detailVO);
        if(detail.getCoverId() != null){
            PortalDocumentInfo coverInfo = portalDocumentInfoService.getById(detail.getCoverId());
            if(coverInfo != null){
                detailVO.setCoverUrl(MinioUtils.getVisitUrl(coverInfo.getResPath()));
            }
        }
        return detailVO;
    }

    @Override
    public IPage<PortalIntangibleInheritorListVO> pageList(IPage<PortalIntangibleInheritorListVO> pageParam, PortalIntangibleInheritorListDTO dto){
        Page<PortalIntangibleInheritorListVO> page = new Page<>(pageParam.getCurrent(), pageParam.getSize());
        IPage<PortalIntangibleInheritorListVO> pageList = baseMapper.selectPageList(page, dto);
        buildPageRecords(pageList);
        return pageList;
    }

    @Override
    public List<PortalIntangibleInheritorListVO> inheritorList(){
        QueryWrapper<PortalIntangibleInheritor> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(PortalIntangibleInheritor::getPublishStatus, 1);
        List<PortalIntangibleInheritor> detailPage = baseMapper.selectList(queryWrapper);
        return detailPage.stream().map(item -> {
            PortalIntangibleInheritorListVO detailVO = new PortalIntangibleInheritorListVO();
            BeanUtils.copyProperties(item, detailVO);
            if(item.getCoverId() != null){
                PortalDocumentInfo coverInfo = portalDocumentInfoService.getById(item.getCoverId());
                if(coverInfo != null){
                    detailVO.setCoverUrl(MinioUtils.getVisitUrl(coverInfo.getResPath()));
                }
            }
            return detailVO;
        }).collect(Collectors.toList());
    }

    private void buildPageRecords(IPage<PortalIntangibleInheritorListVO> page) {
        if (!page.getRecords().isEmpty()) {
            List<PortalIntangibleInheritorListVO> detailPage = page.getRecords();
            List<Long> coverIds = detailPage.stream().map(PortalIntangibleInheritorListVO::getCoverId).filter(Objects::nonNull).collect(Collectors.toList());
            if(CollectionUtil.isNotEmpty(coverIds)){
                List<PortalDocumentInfo> portalDocumentInfos = portalDocumentInfoService.getBaseMapper().selectBatchIds(coverIds);
                Map<Long,String> coverUrlMap = portalDocumentInfos.stream().collect(Collectors.toMap(PortalDocumentInfo::getId, PortalDocumentInfo::getResPath));
                // 查询当前分页中的关联的附件
                page.setRecords(detailPage.stream().peek(item -> {
                    if(item.getCoverId() != null && coverUrlMap.containsKey(item.getCoverId())){
                        item.setCoverUrl(MinioUtils.getVisitUrl(coverUrlMap.get(item.getCoverId())));
                    }
                }).collect(Collectors.toList()));
            }
        }
    }

}




