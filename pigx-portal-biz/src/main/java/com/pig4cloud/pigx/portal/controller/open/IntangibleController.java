package com.pig4cloud.pigx.portal.controller.open;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.pig4cloud.pigx.common.core.util.R;
import com.pig4cloud.pigx.portal.dto.PortalIntangibleActivityListDTO;
import com.pig4cloud.pigx.portal.dto.PortalIntangibleArchiveListDTO;
import com.pig4cloud.pigx.portal.dto.PortalIntangibleInheritorListDTO;
import com.pig4cloud.pigx.portal.dto.PortalIntangibleProjectListDTO;
import com.pig4cloud.pigx.portal.service.PortalIntangibleActivityService;
import com.pig4cloud.pigx.portal.service.PortalIntangibleArchiveService;
import com.pig4cloud.pigx.portal.service.PortalIntangibleInheritorService;
import com.pig4cloud.pigx.portal.service.PortalIntangibleProjectService;
import com.pig4cloud.pigx.portal.vo.*;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 门户网站-非遗文化
 */
@Slf4j
@RestController
@RequestMapping("/open/intangible")
@RequiredArgsConstructor
public class IntangibleController {

    @Resource
    private PortalIntangibleActivityService portalIntangibleActivityService;

    @Resource
    private PortalIntangibleArchiveService portalIntangibleArchiveService;

    @Resource
    private PortalIntangibleInheritorService portalIntangibleInheritorService;

    @Resource
    private PortalIntangibleProjectService portalIntangibleProjectService;

    /**
     * 非遗名录列表
     * @return
     */
    @GetMapping("/projectList")
    public R<List<PortalIntangibleProjectListVO>> projectList() {
        List<PortalIntangibleProjectListVO> result = portalIntangibleProjectService.projectList();
        return R.ok(result);
    }

    /**
     * 非遗名录分页查询
     *
     * @param page 分页对象
     * @return
     */
    @GetMapping("/projectPage")
    public R<IPage<PortalIntangibleProjectListVO>> getPageList(@ParameterObject Page page,
                                                               @ParameterObject PortalIntangibleProjectListDTO dto) {
        dto.setPublishStatus(1);
        return R.ok(portalIntangibleProjectService.pageList(page, dto));
    }

    /**
     * 查询非遗名录详情
     * @return
     */
    @GetMapping("/projectDetail/{id}")
    public R<PortalIntangibleProjectListVO> detail(@PathVariable Long id) {
        return R.ok(portalIntangibleProjectService.detail(id));
    }

    /**
     * 非遗传承人列表
     * @return
     */
    @GetMapping("/inheritorList")
    public R<List<PortalIntangibleInheritorListVO>> inheritorList() {
        List<PortalIntangibleInheritorListVO> result = portalIntangibleInheritorService.inheritorList();
        return R.ok(result);
    }

    /**
     * 非遗传承人分页查询
     *
     * @param page 分页对象
     * @return
     */
    @GetMapping("/inheritorPage")
    public R<IPage<PortalIntangibleInheritorListVO>> getInheritorPage(@ParameterObject Page page,
                                                                 @ParameterObject PortalIntangibleInheritorListDTO dto) {
        dto.setPublishStatus(1);
        return R.ok(portalIntangibleInheritorService.pageList(page, dto));
    }

    /**
     * 查询非遗传承人详情
     * @return
     */
    @GetMapping("/inheritorDetail/{id}")
    public R<PortalIntangibleInheritorListVO> inheritorDetail(@PathVariable Long id) {
        return R.ok(portalIntangibleInheritorService.detail(id));
    }

    /**
     * 非遗活动分页查询
     *
     * @param page 分页对象
     * @return
     */
    @GetMapping("/activityPage")
    public R<IPage<PortalIntangibleActivityListVO>> getActivityPage(@ParameterObject Page page,
                                                                    @ParameterObject PortalIntangibleActivityListDTO dto) {
        dto.setPublishStatus(1);
        return R.ok(portalIntangibleActivityService.pageList(page, dto));
    }

    /**
     * 非遗活动列表
     *
     * @return
     */
    @GetMapping("/getActivityList")
    public R<List<PortalIntangibleActivityListVO>> getActivityList(@RequestParam("startDate") String startDate,
                                                                   @RequestParam("endDate") String endDate) {
        return R.ok(portalIntangibleActivityService.getActivityList(startDate, endDate));
    }

    /**
     * 非遗活动查询详情
     * @return
     */
    @GetMapping("/activityDetail/{id}")
    public R<PortalIntangibleActivityListVO> activityDetail(@PathVariable Long id) {
        return R.ok(portalIntangibleActivityService.detail(id));
    }

    /**
     * 非遗档案列表
     * @return
     */
    @GetMapping("/archiveList")
    public R<List<PortalIntangibleArchiveListVO>> getArchiveList() {
        return R.ok(portalIntangibleArchiveService.getArchiveList());
    }

    /**
     * 非遗档案查询详情
     * @return
     */
    @GetMapping("/archiveDetail/{id}")
    public R<PortalIntangibleArchiveListVO> archiveDetail(@PathVariable Long id) {
        return R.ok(portalIntangibleArchiveService.detail(id));
    }

}
