package com.pig4cloud.pigx.portal.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.pig4cloud.pigx.portal.entity.PortalIntangibleActivity;
import com.pig4cloud.pigx.portal.service.PortalIntangibleActivityService;
import com.pig4cloud.pigx.portal.mapper.PortalIntangibleActivityMapper;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【portal_intangible_activity(非遗活动)】的数据库操作Service实现
* @createDate 2025-08-05 10:28:39
*/
@Service
public class PortalIntangibleActivityServiceImpl extends ServiceImpl<PortalIntangibleActivityMapper, PortalIntangibleActivity>
    implements PortalIntangibleActivityService{

}




