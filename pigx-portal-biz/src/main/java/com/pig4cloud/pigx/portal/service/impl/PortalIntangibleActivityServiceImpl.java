package com.pig4cloud.pigx.portal.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.pig4cloud.pigx.common.security.service.PigxUser;
import com.pig4cloud.pigx.common.security.util.SecurityUtils;
import com.pig4cloud.pigx.portal.dto.PortalIntangibleActivityDTO;
import com.pig4cloud.pigx.portal.dto.PortalIntangibleActivityListDTO;
import com.pig4cloud.pigx.portal.dto.PublishStatusDTO;
import com.pig4cloud.pigx.portal.entity.PortalDocumentInfo;
import com.pig4cloud.pigx.portal.entity.PortalIntangibleActivity;
import com.pig4cloud.pigx.portal.service.PortalDocumentInfoService;
import com.pig4cloud.pigx.portal.service.PortalIntangibleActivityService;
import com.pig4cloud.pigx.portal.mapper.PortalIntangibleActivityMapper;
import com.pig4cloud.pigx.portal.util.MinioUtils;
import com.pig4cloud.pigx.portal.vo.PortalIntangibleActivityListVO;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
* <AUTHOR>
* @description 针对表【portal_intangible_activity(非遗活动)】的数据库操作Service实现
* @createDate 2025-08-05 10:28:39
*/
@Service
public class PortalIntangibleActivityServiceImpl extends ServiceImpl<PortalIntangibleActivityMapper, PortalIntangibleActivity>
    implements PortalIntangibleActivityService{

    @Resource
    private PortalDocumentInfoService portalDocumentInfoService;

    @Override
    public void add(PortalIntangibleActivity portalIntangibleActivity){
        PigxUser user = SecurityUtils.getUser();
        portalIntangibleActivity.setCreator(user.getId());
        portalIntangibleActivity.setModifier(user.getId());
        baseMapper.insert(portalIntangibleActivity);
    }

    @Override
    public void edit(PortalIntangibleActivityDTO dto){
        PortalIntangibleActivity portalIntangibleActivity = baseMapper.selectById(dto.getId());
        if(Objects.nonNull(portalIntangibleActivity)){
            BeanUtils.copyProperties(dto, portalIntangibleActivity);
            PigxUser user = SecurityUtils.getUser();
            portalIntangibleActivity.setModifier(user.getId());
            baseMapper.updateById(portalIntangibleActivity);
        }
    }

    @Override
    public void updatePublishStatus(PublishStatusDTO dto){
        if(CollectionUtil.isNotEmpty(dto.getIds())){
            baseMapper.updatePublishStatus(dto.getIds(), dto.getPublishStatus());
        }
    }

    @Override
    public void delete(List<Long> ids){
        baseMapper.deleteBatchIds(ids);
    }

    @Override
    public PortalIntangibleActivityListVO detail(Long id){
        PortalIntangibleActivityListVO detailVO = new PortalIntangibleActivityListVO();
        PortalIntangibleActivity detail = baseMapper.selectById(id);
        BeanUtil.copyProperties(detail, detailVO);
        if(detail.getCoverId() != null){
            PortalDocumentInfo coverInfo = portalDocumentInfoService.getById(detail.getCoverId());
            if(coverInfo != null){
                detailVO.setCoverUrl(MinioUtils.getVisitUrl(coverInfo.getResPath()));
            }
        }
        return detailVO;
    }

    @Override
    public IPage<PortalIntangibleActivityListVO> pageList(IPage<PortalIntangibleActivityListVO> pageParam, PortalIntangibleActivityListDTO dto){
        Page<PortalIntangibleActivityListVO> page = new Page<>(pageParam.getCurrent(), pageParam.getSize());
        IPage<PortalIntangibleActivityListVO> pageList = baseMapper.selectPageList(page, dto);
        buildPageRecords(pageList);
        return pageList;
    }

    private void buildPageRecords(IPage<PortalIntangibleActivityListVO> page) {
        if (!page.getRecords().isEmpty()) {
            List<PortalIntangibleActivityListVO> detailPage = page.getRecords();
            List<Long> coverIds = detailPage.stream().map(PortalIntangibleActivityListVO::getCoverId).filter(Objects::nonNull).collect(Collectors.toList());
            if(CollectionUtil.isNotEmpty(coverIds)){
                List<PortalDocumentInfo> portalDocumentInfos = portalDocumentInfoService.getBaseMapper().selectBatchIds(coverIds);
                Map<Long,String> coverUrlMap = portalDocumentInfos.stream().collect(Collectors.toMap(PortalDocumentInfo::getId, PortalDocumentInfo::getResPath));
                // 查询当前分页中的关联的附件
                page.setRecords(detailPage.stream().peek(item -> {
                    if(item.getCoverId() != null && coverUrlMap.containsKey(item.getCoverId())){
                        item.setCoverUrl(MinioUtils.getVisitUrl(coverUrlMap.get(item.getCoverId())));
                    }
                }).collect(Collectors.toList()));
            }
        }
    }

    @Override
    public List<PortalIntangibleActivityListVO> getActivityList(String startDay, String endDay){
        QueryWrapper<PortalIntangibleActivity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(PortalIntangibleActivity::getPublishStatus, 1)
                .ge(PortalIntangibleActivity::getActivityTime, startDay)
                .le(PortalIntangibleActivity::getActivityTime, endDay)
                .orderByDesc(PortalIntangibleActivity::getActivityTime);
        List<PortalIntangibleActivity> portalIntangibleActivityList = baseMapper.selectList(queryWrapper);
        return portalIntangibleActivityList.stream().map(item -> {
            PortalIntangibleActivityListVO detailVO = new PortalIntangibleActivityListVO();
            BeanUtils.copyProperties(item, detailVO);
            if(item.getCoverId() != null){
                PortalDocumentInfo coverInfo = portalDocumentInfoService.getById(item.getCoverId());
                if(coverInfo != null){
                    detailVO.setCoverUrl(MinioUtils.getVisitUrl(coverInfo.getResPath()));
                }
            }
            return detailVO;
        }).collect(Collectors.toList());
    }

}




