<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.pig4cloud.pigx.collection.mapper.CoCollectionDetailModifyRecordMapper">

    <resultMap id="coCollectionDetailModifyRecordMap"
               type="com.pig4cloud.pigx.collection.entity.CoCollectionDetailModifyRecordEntity">
        <id property="id" column="id"/>
        <result property="modifyRecord" column="modify_record"/>
        <result property="newCollection" column="new_collection"/>
        <result property="oldCollection" column="old_collection"/>
        <result property="collectionDetailId" column="collection_detail_id"/>
        <result property="createUserId" column="create_user_id"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>
    <resultMap id="detailModifyRecordVoMap" type="com.pig4cloud.pigx.collection.vo.DetailModifyRecordVo">
        <id property="id" column="id"/>
        <result property="modifyRecord" column="modify_record"/>
        <result property="newCollection" column="new_collection"/>
        <result property="oldCollection" column="old_collection"/>
        <result property="collectionDetailId" column="collection_detail_id"/>
        <result property="createUserId" column="create_user_id"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="name" column="name"/>
        <result property="totalRegistrationMark" column="total_registration_mark"/>
    </resultMap>

    <select id="getDetailModifyPage" resultMap="detailModifyRecordVoMap">
        SELECT c.*,name,total_registration_mark FROM co_collection_detail_modify_record c LEFT JOIN co_collection_detail
        cd on c.collection_detail_id = cd.id
        <where>
            <if test="query.totalRegistrationMark != null and query.totalRegistrationMark != ''">
                AND cd.total_registration_mark  like CONCAT(CONCAT('%', #{query.totalRegistrationMark}), '%')
            </if>
            <if test="query.registrationTime != null and query.registrationTime.size() > 0 ">
               and c.create_time BETWEEN #{query.registrationTime[0],jdbcType=TIMESTAMP} AND #{query.registrationTime[1],jdbcType=TIMESTAMP}
            </if>

        </where>
       order by c.create_time desc
    </select>

    <!-- 批量插入-->
    <insert id="batchInsertModifyRecords" parameterType="list">
        INSERT INTO co_collection_detail_modify_record
        (modify_record, new_collection, old_collection, collection_detail_id,
        create_user_id, create_by, create_time, update_by, update_time)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.modifyRecord}, #{item.newCollection}, #{item.oldCollection},
            #{item.collectionDetailId}, #{item.createUserId}, #{item.createBy},
            #{item.createTime}, #{item.updateBy}, #{item.updateTime})
        </foreach>
    </insert>


</mapper>