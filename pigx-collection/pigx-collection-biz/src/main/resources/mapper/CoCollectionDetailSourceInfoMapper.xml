<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.pig4cloud.pigx.collection.mapper.CoCollectionDetailSourceInfoMapper">

  <resultMap id="coCollectionDetailSourceInfoMap" type="com.pig4cloud.pigx.collection.entity.CoCollectionDetailSourceInfoEntity">
        <id property="id" column="id"/>
        <result property="giGivingTime" column="gi_giving_time"/>
        <result property="giDonors" column="gi_donors"/>
        <result property="giRecipient" column="gi_recipient"/>
        <result property="giBonus" column="gi_bonus"/>
        <result property="giDonationUnit" column="gi_donation_unit"/>
        <result property="giAcceptUnit" column="gi_accept_unit"/>
        <result property="gigivingAddress" column="gigiving_address"/>
        <result property="giAcceptAddress" column="gi_accept_address"/>
        <result property="acquTime" column="acqu_time"/>
        <result property="acquSite" column="acqu_site"/>
        <result property="sellUnitAndMan" column="sell_unit_and_man"/>
        <result property="acqucost" column="acqucost"/>
        <result property="acquAcceptUnit" column="acqu_accept_unit"/>
        <result property="exTime" column="ex_time"/>
        <result property="exSellUnit" column="ex_sell_unit"/>
        <result property="exSellMan" column="ex_sell_man"/>
        <result property="exApprovalUnit" column="ex_approval_unit"/>
        <result property="exAcceptUnit" column="ex_accept_unit"/>
        <result property="exHandlers" column="ex_handlers"/>
        <result property="souceOther" column="souce_other"/>
        <result property="souceNote" column="souce_note"/>
        <result property="collectionDetailId" column="collection_detail_id"/>
        <result property="createUserId" column="create_user_id"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
  </resultMap>
</mapper>