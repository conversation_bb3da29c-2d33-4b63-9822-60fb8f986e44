<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.pig4cloud.pigx.collection.mapper.CoCollectionDetailDiscoverInfoMapper">

  <resultMap id="coCollectionDetailDiscoverInfoMap" type="com.pig4cloud.pigx.collection.entity.CoCollectionDetailDiscoverInfoEntity">
        <id property="id" column="id"/>
        <result property="excProject" column="exc_project"/>
        <result property="excaTime" column="exca_time"/>
        <result property="excaSite" column="exca_site"/>
        <result property="excaLeader" column="exca_leader"/>
        <result property="excaOverTime" column="exca_over_time"/>
        <result property="discoverMan" column="discover_man"/>
        <result property="archInstitution" column="arch_institution"/>
        <result property="archAuthority" column="arch_authority"/>
        <result property="archReportName" column="arch_report_name"/>
        <result property="archReportPress" column="arch_report_press"/>
        <result property="archReportDate" column="arch_report_date"/>
        <result property="unearthedState" column="unearthed_state"/>
        <result property="discoverNote" column="discover_note"/>
        <result property="collectionDetailId" column="collection_detail_id"/>
        <result property="createUserId" column="create_user_id"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
  </resultMap>
</mapper>