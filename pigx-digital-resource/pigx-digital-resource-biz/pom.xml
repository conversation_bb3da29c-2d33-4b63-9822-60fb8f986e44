<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
		 xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
		 xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<parent>
		<artifactId>pigx-digital-resource</artifactId>
		<groupId>com.pig4cloud</groupId>
		<version>5.3.0</version>
	</parent>
	<modelVersion>4.0.0</modelVersion>

	<artifactId>pigx-digital-resource-biz</artifactId>
	<packaging>jar</packaging>

	<properties>
		<maven.compiler.source>17</maven.compiler.source>
		<maven.compiler.target>17</maven.compiler.target>
		<metadata-extractor.vcersion>2.18.0</metadata-extractor.vcersion>
		<jjwt.version>0.9.1</jjwt.version>
		<ffmpeg.version>4.4-1.5.6</ffmpeg.version>
		<javacv.version>1.5.6</javacv.version>
		<org.mapstruct.version>1.6.0</org.mapstruct.version>
	</properties>

	<dependencies>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter</artifactId>
		</dependency>

		<!-- mysql -->
		<dependency>
			<groupId>com.mysql</groupId>
			<artifactId>mysql-connector-j</artifactId>
		</dependency>
		<!-- ojdbc8 -->
		<dependency>
			<groupId>com.oracle.database.jdbc</groupId>
			<artifactId>ojdbc8</artifactId>
		</dependency>
		<!--PG-->
		<dependency>
			<groupId>org.postgresql</groupId>
			<artifactId>postgresql</artifactId>
		</dependency>
		<!--mssql-->
		<dependency>
			<groupId>com.microsoft.sqlserver</groupId>
			<artifactId>mssql-jdbc</artifactId>
		</dependency>
		<!--DM8-->
		<dependency>
			<groupId>com.dameng</groupId>
			<artifactId>DmJdbcDriver18</artifactId>
		</dependency>
		<!--upms api、model 模块-->
		<dependency>
			<groupId>com.pig4cloud</groupId>
			<artifactId>pigx-upms-api</artifactId>
		</dependency>
		<!--日志处理-->
		<dependency>
			<groupId>com.pig4cloud</groupId>
			<artifactId>pigx-common-log</artifactId>
		</dependency>
		<dependency>
			<groupId>com.pig4cloud</groupId>
			<artifactId>pigx-common-data</artifactId>
		</dependency>
		<!--swagger-->
		<dependency>
			<groupId>com.pig4cloud</groupId>
			<artifactId>pigx-common-swagger</artifactId>
		</dependency>
		<!--文件系统-->
		<dependency>
			<groupId>com.pig4cloud</groupId>
			<artifactId>pigx-common-oss</artifactId>
		</dependency>
		<dependency>
			<groupId>com.pig4cloud</groupId>
			<artifactId>pigx-flow-task-api</artifactId>
		</dependency>
		<!--注册中心客户端-->
		<dependency>
			<groupId>com.alibaba.cloud</groupId>
			<artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
		</dependency>
		<!--配置中心客户端-->
		<dependency>
			<groupId>com.alibaba.cloud</groupId>
			<artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
		</dependency>
		<!--spring security 、oauth、jwt依赖-->
		<dependency>
			<groupId>com.pig4cloud</groupId>
			<artifactId>pigx-common-security</artifactId>
		</dependency>
		<!--XSS 安全过滤-->
		<dependency>
			<groupId>com.pig4cloud</groupId>
			<artifactId>pigx-common-xss</artifactId>
		</dependency>
		<!-- 字段审计 -->
		<dependency>
			<groupId>com.pig4cloud</groupId>
			<artifactId>pigx-common-audit</artifactId>
		</dependency>
		<!--支持动态路由配置 -->
		<dependency>
			<groupId>com.pig4cloud</groupId>
			<artifactId>pigx-common-gateway</artifactId>
		</dependency>
		<!--sentinel 依赖-->
		<dependency>
			<groupId>com.pig4cloud</groupId>
			<artifactId>pigx-common-sentinel</artifactId>
		</dependency>
		<!--路由控制-->
		<dependency>
			<groupId>com.pig4cloud</groupId>
			<artifactId>pigx-common-gray</artifactId>
		</dependency>
		<!--mybatis-->
		<dependency>
			<groupId>com.baomidou</groupId>
			<artifactId>mybatis-plus-boot-starter</artifactId>
		</dependency>
		<!-- druid 连接池 -->
		<dependency>
			<groupId>com.alibaba</groupId>
			<artifactId>druid-spring-boot-starter</artifactId>
		</dependency>
		<!-- cas sdk -->
		<dependency>
			<groupId>org.jasig.cas.client</groupId>
			<artifactId>cas-client-core</artifactId>
			<version>${cas.sdk.version}</version>
		</dependency>
		<!--旧版api,新版api未包含全部的服务端API的产品能力-->
		<dependency>
			<groupId>com.aliyun</groupId>
			<artifactId>alibaba-dingtalk-service-sdk</artifactId>
			<version>${dingtalk.old.version}</version>
		</dependency>
		<!--企业微信-->
		<dependency>
			<groupId>com.github.binarywang</groupId>
			<artifactId>weixin-java-cp</artifactId>
		</dependency>
		<!--web 模块-->
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-web</artifactId>
		</dependency>
		<!--undertow容器-->
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-undertow</artifactId>
		</dependency>

		<dependency>
			<groupId>io.jsonwebtoken</groupId>
			<artifactId>jjwt</artifactId>
			<version>${jjwt.version}</version>
		</dependency>
		<dependency>
			<groupId>org.apache.pdfbox</groupId>
			<artifactId>pdfbox</artifactId>
			<version>2.0.25</version>
		</dependency>

		<!-- minio -->
		<dependency>
			<groupId>io.minio</groupId>
			<artifactId>minio</artifactId>
			<version>8.0.0</version>
		</dependency>

		<!-- pagehelper -->
		<dependency>
			<groupId>com.github.pagehelper</groupId>
			<artifactId>pagehelper</artifactId>
			<version>5.3.1</version>
		</dependency>

		<dependency>
			<groupId>com.github.pagehelper</groupId>
			<artifactId>pagehelper-spring-boot-autoconfigure</artifactId>
			<version>1.4.3</version>
		</dependency>

		<dependency>
			<groupId>com.github.pagehelper</groupId>
			<artifactId>pagehelper-spring-boot-starter</artifactId>
			<version>1.4.3</version>
		</dependency>

		<dependency>
			<groupId>org.redisson</groupId>
			<artifactId>redisson-spring-boot-starter</artifactId>
			<version>3.17.1</version>
		</dependency>
		<!--图片压缩-->
		<dependency>
			<groupId>net.coobird</groupId>
			<artifactId>thumbnailator</artifactId>
			<version>0.4.19</version>
		</dependency>
		<!--加解密组件-->
		<dependency>
			<groupId>cn.hutool</groupId>
			<artifactId>hutool-crypto</artifactId>
		</dependency>
		<dependency>
			<groupId>com.drewnoakes</groupId>
			<artifactId>metadata-extractor</artifactId>
			<version>${metadata-extractor.vcersion}</version>
		</dependency>
		<dependency>
			<groupId>com.daspatial.gis</groupId>
			<artifactId>gisadmin-common</artifactId>
			<version>3.8.5</version>
			<exclusions>
				<exclusion>
					<artifactId>dynamic-datasource-spring-boot-starter</artifactId>
					<groupId>com.baomidou</groupId>
				</exclusion>
			</exclusions>
		</dependency>

		<!--	redis 分布式锁	-->
		<dependency>
			<groupId>com.baomidou</groupId>
			<artifactId>lock4j-redis-template-spring-boot-starter</artifactId>
			<version>2.2.2</version>
		</dependency>

		<dependency>
			<groupId>org.elasticsearch.client</groupId>
			<artifactId>elasticsearch-rest-high-level-client</artifactId>
			<version>7.10.0</version>
		</dependency>
		<dependency>
			<groupId>org.elasticsearch</groupId>
			<artifactId>elasticsearch</artifactId>
			<version>7.10.0</version>
		</dependency>
		<dependency>
			<groupId>org.elasticsearch</groupId>
			<artifactId>elasticsearch-x-content</artifactId>
			<version>7.10.0</version>
		</dependency>
		<dependency>
			<groupId>org.mapstruct</groupId>
			<artifactId>mapstruct</artifactId>
			<version>${org.mapstruct.version}</version>
		</dependency>
		<dependency>
			<groupId>co.elastic.clients</groupId>
			<artifactId>elasticsearch-java</artifactId>
			<version>8.12.0</version>
		</dependency>
		<dependency>
			<groupId>org.elasticsearch.client</groupId>
			<artifactId>elasticsearch-rest-client</artifactId>
			<version>8.12.0</version>
		</dependency>
		<dependency>
			<groupId>jakarta.json</groupId>
			<artifactId>jakarta.json-api</artifactId>
			<version>2.0.1</version>
		</dependency>
		<dependency>
			<groupId>org.apache.pdfbox</groupId>
			<artifactId>pdfbox</artifactId>
			<version>2.0.24</version>
		</dependency>
		<dependency>
			<groupId>org.bytedeco</groupId>
			<artifactId>ffmpeg-platform</artifactId>
			<version>${ffmpeg.version}</version>
		</dependency>
		<dependency>
			<groupId>org.bytedeco</groupId>
			<artifactId>javacv</artifactId>
			<version>${javacv.version}</version>
		</dependency>

		<dependency>
			<groupId>com.pig4cloud</groupId>
			<artifactId>pigx-digital-resource-api</artifactId>
			<version>${version}</version>
		</dependency>
    </dependencies>

	<build>
		<plugins>
			<plugin>
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-maven-plugin</artifactId>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-deploy-plugin</artifactId>
				<configuration>
					<skip>true</skip>
				</configuration>
			</plugin>
			<plugin>
				<groupId>io.fabric8</groupId>
				<artifactId>docker-maven-plugin</artifactId>
				<configuration>
					<skip>false</skip>
				</configuration>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-compiler-plugin</artifactId>
				<configuration>
					<source>17</source>
					<target>17</target>
					<annotationProcessorPaths>
						<path>
							<groupId>org.projectlombok</groupId>
							<artifactId>lombok</artifactId>
							<version>1.18.20</version>
						</path>
						<path>
							<groupId>org.mapstruct</groupId>
							<artifactId>mapstruct-processor</artifactId>
							<version>${org.mapstruct.version}</version>
						</path>
					</annotationProcessorPaths>
				</configuration>
			</plugin>
		</plugins>
		<resources>
			<resource>
				<directory>src/main/resources</directory>
				<filtering>true</filtering>
				<excludes>
					<exclude>**/*.xlsx</exclude>
					<exclude>**/*.xls</exclude>
				</excludes>
			</resource>
			<resource>
				<directory>src/main/resources</directory>
				<filtering>false</filtering>
				<includes>
					<include>**/*.xlsx</include>
					<include>**/*.xls</include>
				</includes>
			</resource>
		</resources>
	</build>
	<repositories>
		<repository>
			<id>icdd</id>
			<url>http://repo.internal.daspatial.com/repository/maven-public/</url>
			<releases>
				<enabled>true</enabled>
			</releases>
			<snapshots>
				<enabled>true</enabled>
			</snapshots>
		</repository>
	</repositories>


</project>