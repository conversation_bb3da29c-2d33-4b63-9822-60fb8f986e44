server:
  port: 5678

spring:
  application:
    name: @artifactId@
  cloud:
    nacos:
      username: @nacos.username@
      password: @nacos.password@
      discovery:
        #server-addr: ${NACOS_HOST:pigx-register}:${NACOS_PORT:8848}
        server-addr: *************:8848
        namespace: cd930b02-ee47-46db-87bb-fcd4cf6c23ac
      config:
        server-addr: ${spring.cloud.nacos.discovery.server-addr}
        namespace: cd930b02-ee47-46db-87bb-fcd4cf6c23ac
  config:
    import:
      - optional:nacos:<EMAIL>@.yml
      - optional:nacos:${spring.application.name}-@profiles.active@.yml



