<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pig4cloud.pigx.data.center.mapper.SecurityLevelMapper">


    <update id="resortOfTwo">
        update security_level
        set sort_order = case id
                             when #{firstId}
                                 then (select sort_order from security_level where id = #{secondId})
                             when #{secondId}
                                 then (select sort_order from security_level where id = #{firstId}) end
        where del_flag = '0'
          and id in (#{firstId}, #{secondId})
    </update>


    <select id="getSecurityLevel" resultType="com.pig4cloud.pigx.data.center.entity.dto.SecurityLevelDTO">
        select sl.enabled, sl.sort_order, la.security_id, la.visible, la.function_access, ap.dept_id
        from security_level sl
        left join security_level_access la on sl.id = la.security_id
        left join security_access_post ap on ap.access_id = la.id
        <where>
            <if test="postIdList != null and postIdList.size() > 0">
                <foreach collection="postIdList" item="item" separator=" OR " open="(" close=")">
                    FIND_IN_SET(#{item}, post_ids)
                </foreach>
            </if>
        </where>
    </select>


    <select id="getOneIncludeDeleted" resultType="com.pig4cloud.pigx.data.center.entity.SecurityLevel">
        select  * from security_level where id = #{id}
    </select>

</mapper>