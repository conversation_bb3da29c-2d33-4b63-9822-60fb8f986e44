<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.pig4cloud.pigx.data.center.mapper.DatabaseInfoMapper">

  <resultMap id="databaseInfoMap" type="com.pig4cloud.pigx.data.center.entity.DatabaseInfo">
        <id property="id" column="id"/>
        <result property="comment" column="comment"/>
        <result property="extraInfo" column="extra_info"/>
        <result property="name" column="name"/>
        <result property="owner" column="owner"/>
        <result property="datasourceId" column="datasource_id"/>
        <result property="createTime" column="create_time"/>
  </resultMap>
</mapper>