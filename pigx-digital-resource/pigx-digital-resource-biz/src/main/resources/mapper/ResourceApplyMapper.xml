<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pig4cloud.pigx.data.center.mapper.ResourceApplyMapper">


    <select id="applyTotal" resultType="com.pig4cloud.pigx.data.center.vo.resourceapply.ResourceApplyTotalVO">
		select app.apply_status,
		       app.resource_id,
		       ass.business_id,
		       ass.first_catalog_no,
		       ass.second_catalog_no,
			   app.create_by
		from resource_apply app
			left join data_assets ass on app.resource_id = ass.business_id
		where app.apply_status != 4 and ass.business_id is not null
		<if test="timeType == 1 || timeType == 2">
			and DATE_FORMAT(app.create_time,'YYYY-MM-DD') &gt;= #{begin}
		</if>
	</select>


</mapper>