<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pig4cloud.pigx.data.center.mapper.ResourceActionMapper">

    <insert id="batchInsert">
        INSERT INTO resource_action (resource_id, action_type, tab_name, create_by, create_time, update_by, update_time,
        remark,type)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.resourceId}, #{item.actionType}, #{item.tabName}, #{item.createBy}, #{item.createTime},
            #{item.updateBy}, #{item.updateTime}, #{item.remark},#{item.type})
        </foreach>
    </insert>



    <update id="resumeResourceAction" parameterType="java.util.Map">
        UPDATE resource_action SET del_flag = '0' WHERE del_flag = '1' and
        <foreach item="item" collection="list" separator=" or " open="(" close=")">
          tab_name = #{item.tableName} and resource_id IN
          <foreach item="item2"   collection="item.resourceIds" separator="," open="(" close=") ">
              #{item2}
          </foreach>
        </foreach>
    </update>

	<select id="sumCount" resultType="Long">
		select count(1) from resource_action where resource_action.action_type = 7
	</select>

	<select id="todayCount" resultType="Long">
		select count(1) from resource_action where resource_action.action_type = 7 and DATE_FORMAT(create_time, '%Y-%m-%d') = #{date};
	</select>

	<select id="thirtyCount" resultType="Long">
        SELECT COUNT(1) FROM resource_action
        WHERE resource_action.action_type = 7
          AND create_time > #{date}
	</select>

</mapper>