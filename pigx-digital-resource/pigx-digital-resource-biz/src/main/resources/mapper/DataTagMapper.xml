<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.pig4cloud.pigx.data.center.mapper.DataTagMapper">

  <resultMap id="dataTagMap" type="com.pig4cloud.pigx.data.center.entity.DataTag">
        <id property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="parentId" column="parent_id"/>
        <result property="createUser" column="create_user"/>
        <result property="status" column="status"/>
        <result property="num" column="num"/>
        <result property="remark" column="remark"/>
        <result property="createTime" column="create_time"/>
  </resultMap>
</mapper>