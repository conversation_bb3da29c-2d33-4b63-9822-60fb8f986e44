<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.pig4cloud.pigx.data.center.mapper.ColumnCustomeRuleMapper">

    <resultMap id="catalog" type="com.pig4cloud.pigx.data.center.entity.ColumnCustomeRule">
        <id property="id" column="id"/>
        <result property="type" column="type"/>
        <result property="typeName" column="type_name"/>
        <result property="typeCode" column="type_code"/>
        <result property="rule" column="rule"/>
    </resultMap>
    <select id="getByTypeCode" resultType="com.pig4cloud.pigx.data.center.entity.ColumnCustomeRule">
        select * from t_column_custome_rule where type_code = #{typeCode}
    </select>

</mapper>
