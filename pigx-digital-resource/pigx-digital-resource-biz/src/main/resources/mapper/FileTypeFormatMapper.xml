<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.pig4cloud.pigx.data.center.mapper.FileTypeFormatMapper">

    <resultMap id="filetype" type="com.pig4cloud.pigx.data.center.entity.FileTypeFormat">
        <id column="id" property="id"></id>
        <result column="format" property="format"></result>
        <result column="type" property="type"></result>
        <result column="type_name" property="typeName"></result>
    </resultMap>
    <select id="getByFormat" resultMap="filetype">
        select * from t_file_type_format where format = #{format} limit 1
    </select>

    <select id="getTypeByFormats" resultMap="filetype">
        select * from t_file_type_format where format in(
        <foreach collection="formats" item="format" separator=",">
            #{format}
        </foreach>)
    </select>

</mapper>
