<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.pig4cloud.pigx.data.center.mapper.MetaDataManageMapper">

    <resultMap id="metaDataGenerate" type="com.pig4cloud.pigx.data.center.entity.MetaDataManage">
        <id property="id" column="id"/>
    </resultMap>
    <select id="getByMetaDataId" resultType="com.pig4cloud.pigx.data.center.entity.MetaDataManage">
        select * from t_meta_data_manage_info where meta_data_id = #{metaDataId} limit 1
    </select>

</mapper>
