<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.pig4cloud.pigx.data.center.mapper.ArchiveMapper">

    <insert id="save" parameterType="com.pig4cloud.pigx.data.center.controller.request.archive.ArchiveData"
            useGeneratedKeys="true" keyProperty="id">
        insert into ${tableName} (
        <foreach collection="archiveKvList" item="item" index="index" separator=",">
            ${item.columnName}
        </foreach>) values
        (<foreach collection="archiveKvList" item="item" index="index" separator=",">
        ${item.realValue}
    </foreach>)
    </insert>

    <update id="update">
        update ${tableName}
        <set>
            <foreach collection="archiveKvList" item="item" index="index" separator=",">
                ${item.columnName} = ${item.realValue}
            </foreach>
        </set>
        where id = #{id}
    </update>

    <select id="pageArchive" resultType="java.util.Map"
            parameterType="com.pig4cloud.pigx.data.center.controller.request.archive.ArchiveQry">
        select a.*,b.catalog_name,c.security_name from ${tableName} a
        left JOIN t_catalog b ON a.catalog_no=b.catalog_no
        left join security_level c ON a.security_id=c.id
        <where>
            <choose>
                <when test="queryType == 1">
                    and a.collect_delete != 1
                </when>
                <otherwise>
                    and a.manage_delete != 1
                </otherwise>
            </choose>

            <if test="archiveState != null">
                and a.archive_state = #{archiveState}
            </if>
            <if test="createTimeStart != null and createTimeStart!=''">
                and a.create_time &gt;= TO_TIMESTAMP(#{createTimeStart},'YYYY-MM-DD HH24:MI:SS')
            </if>
            <if test="createTimeEnd != null and createTimeEnd!=''">
                and a.create_time &lt; TO_TIMESTAMP(#{createTimeEnd},'YYYY-MM-DD HH24:MI:SS') + INTERVAL '1 day'
            </if>
            <if test="title != null and title !=''">
                and a.assets_name like concat('%',#{title},'%')
            </if>
            <if test="arcCode != null and arcCode !=''">
                and a.arc_code like concat('%',#{arcCode},'%')
            </if>
            <if test="catalogNo != null and catalogNo !=''">
                and a.catalog_no =#{catalogNo}
            </if>
            <if test="years != null and years!=''">
                and a.years = #{years}
            </if>
            <if test="ids!=null and ids.size()>0">
                and a.id in
                (
                <foreach collection="ids" item="id" separator=",">
                    #{id}
                </foreach>)
            </if>
            <if test="deptId != null">
                and a.filing_dep_id = #{deptId}
            </if>
            <if test="fillStartDate != null">
                and a.filing_date &gt;= #{fillStartDate}
            </if>
            <if test="fillEndDate != null">
                and a.filing_date &lt; #{fillEndDate}
            </if>

        </where>
        order by a.create_time desc
    </select>

    <delete id="delArchive">
        update ${tableName}
        set collect_delete=1
        where id = #{id}
    </delete>

    <delete id="batchDelArchive">
        update ${tableName} set collect_delete=1 where id in
        (
        <foreach collection="ids" item="id" separator=",">
            #{id}
        </foreach>)
    </delete>

    <delete id="batchDelArchiveEnd">
        update ${tableName} set collect_delete=1 , manage_delete=1 where id in
        (
        <foreach collection="ids" item="id" separator=",">
            #{id}
        </foreach>)
    </delete>


    <update id="batchArchive">
        update ${tableName}
        set
        archive_state = #{archiveState},
        <!--
                filing_dep_id=#{deptId},
        -->
        archiver = #{archiver},
        <!--        filing_date=to_date(#{fillDate},'YYYY-MM-DD HH:MI:SS')-->
        filing_date=#{fillDate}
        where id in
        (
        <foreach collection="ids" item="id" separator=",">
            #{id}
        </foreach>)
    </update>

    <select id="archiveDetail" resultType="java.util.Map">
        select *
        from ${tableName}
        where id = #{id}
    </select>

    <update id="updateOperator">
        update ${tableName}
        set archive_user_id=#{userId}
        where id = #{id}
    </update>

    <update id="updateIdentifyType">
        update ${tableName}
        set identify_type=#{type}
        where id = #{id}
    </update>
</mapper>
