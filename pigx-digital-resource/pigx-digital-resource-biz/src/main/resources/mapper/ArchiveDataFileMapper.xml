<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.pig4cloud.pigx.data.center.mapper.ArchiveDataFileMapper">
    <resultMap id="archiveDataFile" type="com.pig4cloud.pigx.data.center.entity.ArchiveDataFile">
        <id column="id" property="id"/>
        <result column="archive_id" property="archiveId"/>
        <result column="resource_file_id" property="resourceFileId"/>
        <result column="archive_table_name" property="archiveTableName"/>
    </resultMap>
    <select id="getByFileId" resultType="java.lang.Integer">
        select 1 from archive_data_file where archive_id = #{archiveId} and resource_file_id = #{resourceFileId} and
        archive_table_name = #{tableName}
    </select>
</mapper>
