<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.pig4cloud.pigx.data.center.mapper.DataAssetsMapper">

    <resultMap id="dataAssetsMap" type="com.pig4cloud.pigx.data.center.entity.DataAssets">
        <id property="id" column="id"/>
        <result property="tableName" column="table_name"/>
        <result property="businessId" column="business_id"/>
        <result property="assetsUserId" column="assets_user_id"/>
        <result property="assetsDepId" column="assets_dep_id"/>
        <result property="securityLevel" column="security_level"/>
        <result property="assetsType" column="assets_type"/>
        <result property="name" column="name"/>
        <result property="assetsNum" column="assets_num"/>
        <result property="createTime" column="create_time"/>
    </resultMap>
    <select id="createTable">
        ${sql}
    </select>
    <select id="createCommont">
        ${sql}
    </select>
    <select id="pageMetaData"
            resultType="com.pig4cloud.pigx.data.center.controller.request.metadata.MetaDataAdd"
            parameterType="com.pig4cloud.pigx.data.center.controller.request.metadata.MetaDataQry">
        select
        da.id ,
        da.table_name ,
        da.assets_user_id ,
        da.assets_dep_id ,
        da.name ,
        da.assets_num ,
        da.remark ,
        da.use_state,
        da.catalog_no ,
        da.create_time ,
        da.publish_state ,
        tmdi.meta_data_id,
        tmdi.report_mode,
        tmdi.report_frequency,
        tmdi.admin_tel,
        tmdi.report_remark,
        tmdi.admin_name,
        tmdi.first_report_tm,
        tmdi.last_report_tm,
        tmdi.report_state
        from data_assets da,t_meta_data_manage_info tmdi
        where da.id = tmdi.meta_data_id and da.assets_type = #{assetsType} and da.catalog_no = #{catalogNo} and
        da.use_state != 2
        <if test="name != null and name != ''">
            and da.name like concat('%',#{name},'%')
        </if>
        <if test="assetsDepId != null">
            and da.assets_dep_id = #{assetsDepId}
        </if>
        <if test="useState != null">
            and da.use_state = #{useState}
        </if>
        <if test="publishState != null">
            and da.publish_state = #{publishState}
        </if>
        order by da.id desc
    </select>

    <update id="renameTable">
        ${sql}
    </update>

    <insert id="insertGeometry" parameterType="com.pig4cloud.pigx.data.center.entity.DataAssets" keyColumn="id"
            keyProperty="id" useGeneratedKeys="true">
        insert into data_assets
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="table_name != null">
                table_name,
            </if>
            <if test="business_id != null">
                business_id,
            </if>
            <if test="assets_user_id != null">
                assets_user_id,
            </if>
            <if test="assets_dep_id != null">
                assets_dep_id,
            </if>
            <if test="security_level != null">
                security_level,
            </if>
            <if test="assets_type != null">
                assets_type,
            </if>
            <if test="create_time != null">
                create_time,
            </if>
            <if test="name != null">
                name,
            </if>
            <if test="assets_num != null">
                assets_num,
            </if>
            <if test="data_region != null">
                data_region,
            </if>
            <if test="remark != null">
                remark,
            </if>
            <if test="catalog_no != null">
                catalog_no,
            </if>
            <if test="use_state != null">
                use_state,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="tableName != null">
                #{tableName,jdbcType=VARCHAR},
            </if>
            <if test="businessId != null">
                #{businessId,jdbcType=BIGINT},
            </if>
            <if test="assetsUserId != null">
                #{assetsUserId,jdbcType=BIGINT},
            </if>
            <if test="assetsDepId != null">
                #{assetsDepId,jdbcType=BIGINT},
            </if>
            <if test="securityLevel != null">
                #{securityLevel,jdbcType=INTEGER},
            </if>
            <if test="assetsType != null">
                #{assetsType,jdbcType=INTEGER},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>

            <if test="name != null">
                #{name,jdbcType=VARCHAR},
            </if>
            <if test="assetsNum != null">
                #{assetsNum,jdbcType=VARCHAR},
            </if>
            <if test="dataRegion != null">
                ST_GeomFromText(#{dataRegion,jdbcType=VARCHAR}, 4326),
            </if>
            <if test="remark != null">
                #{remark,jdbcType=VARCHAR},
            </if>
            <if test="catalogNo != null">
                #{catalogNo},
            </if>
            <if test="useState != null">
                #{useState,jdbcType=INTEGER},
            </if>
        </trim>
    </insert>


    <update id="insertGeometry" parameterType="com.pig4cloud.pigx.data.center.entity.DataAssets">
        update data_assets
        <set>
            <if test="table_name != null">
                table_name = #{tableName,jdbcType=VARCHAR},
            </if>
            <if test="business_id != null">
                business_id = #{businessId,jdbcType=BIGINT},
            </if>
            <if test="assets_user_id != null">
                assets_user_id = #{assetsUserId,jdbcType=BIGINT},
            </if>
            <if test="assets_dep_id != null">
                assets_dep_id = #{assetsDepId,jdbcType=BIGINT},
            </if>
            <if test="security_level != null">
                security_level = #{securityLevel,jdbcType=INTEGER},
            </if>
            <if test="assets_type != null">
                assets_type = #{assetsType,jdbcType=INTEGER},
            </if>
            <if test="create_time != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="name != null">
                name = #{name,jdbcType=VARCHAR},
            </if>
            <if test="assets_num != null">
                assets_num = #{assetsNum,jdbcType=VARCHAR},
            </if>
            <if test="data_region != null">
                data_region = ST_GeomFromText(#{dataRegion,jdbcType=VARCHAR}, 4326),
            </if>
            <if test="remark != null">
                remark = #{remark,jdbcType=VARCHAR},
            </if>
            <if test="catalog_no != null">
                catalog_no = #{catalogNo},
            </if>
            <if test="use_state != null">
                use_state = #{useState,jdbcType=INTEGER},
            </if>
            where id = #{id,jdbcType=BIGINT}
        </set>

    </update>

    <select id="getByAssetNo" resultType="com.pig4cloud.pigx.data.center.entity.DataAssets">
        select
        da.id ,
        da.table_name ,
        da.assets_user_id ,
        da.assets_dep_id ,
        da.name ,
        da.assets_num ,
        da.remark ,
        da.use_state,
        da.catalog_no ,
        da.create_time ,
        da.publish_state
        from data_assets da
        where da.assets_num = #{assetNum} and da.catalog_no = #{catalogNo} and da.use_state != 2 limit 1
    </select>

    <select id="getByCatalogNos" resultType="com.pig4cloud.pigx.data.center.entity.DataAssets">
        select
        da.id ,
        da.table_name ,
        da.assets_user_id ,
        da.assets_dep_id ,
        da.name ,
        da.assets_num ,
        da.remark ,
        da.use_state,
        da.catalog_no ,
        da.create_time ,
        da.publish_state
        from data_assets da
        where da.catalog_no in(
        <foreach collection="catalogNos" item="catalogNo" separator=",">
            #{catalogNo}
        </foreach>)
        and da.use_state != 2 and da.assets_type=4 and da.publish_state = 1
    </select>
    <select id="getByTableName" resultType="com.pig4cloud.pigx.data.center.entity.DataAssets">
        select
        da.id ,
        da.table_name ,
        da.assets_user_id ,
        da.assets_dep_id ,
        da.name ,
        da.assets_num ,
        da.remark ,
        da.use_state,
        da.catalog_no ,
        da.create_time ,
        da.publish_state
        from data_assets da
        where da.table_name = #{tableName} and da.use_state != 2 and da.assets_type=4 limit 1
    </select>
    <select id="countByCatalog" resultType="com.pig4cloud.pigx.data.center.es.response.CountResult"
            parameterType="com.pig4cloud.pigx.data.center.entity.query.ModelDataQuery">
        select
        count(da.id) as itemCount,
        da.first_catalog_no as itemNo
        from data_assets da
        where da.use_state != 2
        <if test="catalogNo != null and catalogNo!=''">
            and da.catalog_no = #{catalogNo}
        </if>
        <if test="startTime != null">
            and create_time &gt;= #{startTime}
        </if>
        <if test="endTime != null">
            and create_time &lt;= #{endTime}
        </if>
        <if test="deptId != null">
            and da.assets_dep_id = #{deptId}
        </if>
        <if test="assetsType != null">
            and da.assets_type = #{assetsType}
        </if>
        group by da.first_catalog_no
    </select>
    <select id="countMetaData" resultType="java.lang.Long"
            parameterType="com.pig4cloud.pigx.data.center.entity.query.ModelDataQuery">
        select count(1) from data_assets da where da.use_state!=2 and assets_type=4
        <if test="deptId != null">
            and da.assets_dep_id = #{deptId}
        </if>
        <if test="catalogNo != null and catalogNo!=''">
            and da.catalog_no = #{catalogNo}
        </if>
        <if test="startTime != null">
            and create_time &gt;= #{startTime}
        </if>
        <if test="endTime != null">
            and create_time &lt;= #{endTime}
        </if>
    </select>
    <select id="countResource" resultType="java.lang.Long"
            parameterType="com.pig4cloud.pigx.data.center.entity.query.ModelDataQuery">
        select count(1) from data_assets da where da.use_state!=2
        <if test="assetsType != null">
            and da.assets_type = #{assetsType}
        </if>
        <if test="deptId != null">
            and da.assets_dep_id = #{deptId}
        </if>
        <if test="catalogNo != null and catalogNo!=''">
            and da.catalog_no = #{catalogNo}
        </if>
        <if test="startTime != null">
            and create_time &gt;= #{startTime}
        </if>
        <if test="endTime != null">
            and create_time &lt;= #{endTime}
        </if>
    </select>
    <select id="countSize" resultType="java.lang.Long"
            parameterType="com.pig4cloud.pigx.data.center.entity.query.ModelDataQuery">
        select COALESCE(SUM(da.size), 0) from data_assets da where da.use_state!=2
        <if test="deptId != null">
            and da.assets_dep_id = #{deptId}
        </if>
        <if test="catalogNo != null and catalogNo!=''">
            and da.catalog_no = #{catalogNo}
        </if>
        <if test="startTime != null">
            and create_time &gt;= #{startTime}
        </if>
        <if test="endTime != null">
            and create_time &lt;= #{endTime}
        </if>
    </select>
    <select id="countTodayResource" resultType="java.lang.Long"
            parameterType="com.pig4cloud.pigx.data.center.entity.query.ModelDataQuery">
        select count(1) from data_assets da where da.use_state != 2
        <if test="startTime != null">
            and create_time &gt;= #{startTime}
        </if>
        <if test="endTime != null">
            and create_time &lt;= #{endTime}
        </if>
        <if test="assetsType != null">
            and da.assets_type = #{assetsType}
        </if>
        <if test="deptId != null">
            and da.assets_dep_id = #{deptId}
        </if>
        <if test="catalogNo != null and catalogNo!=''">
            and da.catalog_no = #{catalogNo}
        </if>
    </select>
    <select id="countFirstCatalog" resultType="com.pig4cloud.pigx.data.center.es.response.CountResult"
            parameterType="com.pig4cloud.pigx.data.center.controller.request.archive.CountQry">
        select
        count(da.id) as itemCount,
        da.first_catalog_no as itemNo
        from data_assets da
        where  first_catalog_no is not null
        and use_state = 1
        <if test="dataAssetsType != null">
            and da.assets_type=#{dataAssetsType}
        </if>
        group by da.first_catalog_no
    </select>
    <select id="trendChart" resultType="com.pig4cloud.pigx.data.center.es.response.CountResult"
            parameterType="com.pig4cloud.pigx.data.center.entity.query.ModelDataQuery">
        select
        count(da.id) as itemCount,
        DATE_FORMAT(create_time, '%Y-%m') as itemName
        from data_assets da
        where da.use_state != 2
        <if test="assetsType != null">
            and da.assets_type = #{assetsType}
        </if>
        <if test="deptId != null">
            and da.assets_dep_id = #{deptId}
        </if>
        <if test="catalogNo != null and catalogNo!=''">
            and da.catalog_no = #{catalogNo}
        </if>
        <if test="startTime != null">
            and create_time &gt;= #{startTime}
        </if>
        <if test="endTime != null">
            and create_time &lt;= #{endTime}
        </if>
        group by itemName order by itemName asc
    </select>
    <select id="deptRange" resultType="com.pig4cloud.pigx.data.center.es.response.CountResult"
            parameterType="com.pig4cloud.pigx.data.center.entity.query.ModelDataQuery">
        select
        count(da.id) as itemCount,
        da.assets_dep_id as id
        from data_assets da
        where da.use_state != 2
        <if test="assetsType != null">
            and da.assets_type = #{assetsType}
        </if>
        <if test="startTime != null">
            and create_time &gt;= #{startTime}
        </if>
        <if test="endTime != null">
            and create_time &lt;= #{endTime}
        </if>
        group by da.assets_dep_id
    </select>
    <select id="countBySecurity" resultType="com.pig4cloud.pigx.data.center.es.response.CountResult"
            parameterType="com.pig4cloud.pigx.data.center.entity.query.ModelDataQuery">
        select
        count(da.id) as itemCount,
        da.security_level as id
        from data_assets da
        where da.use_state != 2
        <if test="assetsType != null">
            and da.assets_type = #{assetsType}
        </if>
        <if test="deptId != null">
            and da.assets_dep_id = #{deptId}
        </if>
        <if test="startTime != null">
            and create_time &gt;= #{startTime}
        </if>
        <if test="endTime != null">
            and create_time &lt;= #{endTime}
        </if>
        group by da.security_level
    </select>
    <select id="countByFileType" resultType="com.pig4cloud.pigx.data.center.es.response.CountResult"
            parameterType="com.pig4cloud.pigx.data.center.entity.query.ModelDataQuery">
        select
        count(da.id) as itemCount,
        da.file_type as itemNo
        from data_assets da
        where da.use_state != 2
        <if test="assetsType != null">
            and da.assets_type = #{assetsType}
        </if>
        <if test="deptId != null">
            and da.assets_dep_id = #{deptId}
        </if>
        <if test="startTime != null">
            and create_time &gt;= #{startTime}
        </if>
        <if test="endTime != null">
            and create_time &lt;= #{endTime}
        </if>
        group by da.file_type
    </select>

    <select id="getByTableAndId" resultType="com.pig4cloud.pigx.data.center.entity.DataAssets">
        select *
        from data_assets
        where table_name = #{tableName} and business_id = #{id} limit 1
    </select>

    <update id="updateByTableAndId">
        update data_assets
        set use_state = 2
        where table_name = #{tableName} and business_id = #{id}
    </update>

    <update id="batchUpdateByTableAndId">
        update data_assets
        set use_state = 2
        where table_name = #{tableName} and business_id in(
        <foreach collection="ids" item="id" separator=",">
            #{id}
        </foreach>)
    </update>

    <select id="archiveTrendChart" resultType="com.pig4cloud.pigx.data.center.es.response.CountResult">
        select
        count(da.id) as itemCount,
        DATE_FORMAT(create_time, '%m-%d') as itemName
        from data_assets da
        where da.use_state != 2
        <if test="assetsType != null">
            and da.assets_type = #{assetsType}
        </if>
        <if test="deptId != null">
            and da.assets_dep_id = #{deptId}
        </if>
        <if test="catalogNo != null and catalogNo!=''">
            and da.catalog_no = #{catalogNo}
        </if>
        <if test="startTime != null">
            and create_time &gt;= #{startTime}
        </if>
        <if test="endTime != null">
            and create_time &lt;= #{endTime}
        </if>
        group by itemName order by itemName asc
    </select>
    <select id="getCatalogName" resultType="java.util.Map">
        select a.table_name,b.catalog_name from data_assets a,t_catalog b where a.catalog_no =b.catalog_no and
        a.use_state != 2
        and a.table_name in(
        <foreach collection="tableNames" item="tableName" separator=",">
            #{tableName}
        </foreach>)
        group by b.catalog_name, a.table_name
    </select>
    <select id="countPublishResource" resultType="java.lang.Long"
            parameterType="com.pig4cloud.pigx.data.center.entity.query.ModelDataQuery">
        select count(1) from data_assets da where da.use_state!=2
        and publish_time &gt;= #{startTime}
        and publish_time &lt;= #{endTime}
        <if test="assetsType != null">
            and da.assets_type = #{assetsType}
        </if>
        <if test="deptId != null">
            and da.assets_dep_id = #{deptId}
        </if>
        <if test="catalogNo != null and catalogNo!=''">
            and da.catalog_no = #{catalogNo}
        </if>
    </select>
    <select id="countResourceByTableName" resultType="com.pig4cloud.pigx.data.center.es.response.CountResult">
        select count(1) as itemCount,table_name as itemNo from data_assets where use_state!=2 and assets_type = 2
        and table_name in(
        <foreach collection="tableNames" item="tableName" separator=",">
            #{tableName}
        </foreach>)
        group by table_name
    </select>

    <select id="checkByCatalog" resultType="java.lang.Integer">
        select 1 from data_assets where use_state!=2 and
        (catalog_no = #{catalogNo} or second_catalog_no = #{catalogNo} or first_catalog_no = #{catalogNo})
        limit 1
    </select>

    <select id="dataPage" resultMap="dataAssetsMap">
        select * from data_assets da where 1=1
        <if test="assetsType != null and assetsType!=''">
            and da.assets_type = #{assetsType}
        </if>
        <if test="name != null and name!=''">
            and da.name like concat('%',#{name},'%')
        </if>
    </select>
</mapper>