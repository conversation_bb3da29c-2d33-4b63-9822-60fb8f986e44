<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.pig4cloud.pigx.data.center.mapper.CatalogMapper">

    <resultMap id="catalog" type="com.pig4cloud.pigx.data.center.entity.Catalog">
        <id property="id" column="id"/>
        <result property="catalogNo" column="catalog_no"/>
        <result property="catalogState" column="catalog_state"/>
        <result property="catalogSort" column="catalog_sort"/>
        <result property="catalogRemark" column="catalog_remark"/>
        <result property="catalogParentNo" column="catalog_parent_no"/>
        <result property="catalogName" column="catalog_name"/>
        <result property="catalogLevel" column="catalog_level"/>
        <result property="catalogCreateTm" column="catalog_create_tm"/>
        <result property="catalogCreator" column="catalog_creator"/>
        <result property="catalogType" column="catalog_type"/>
    </resultMap>

    <select id="list" parameterType="com.pig4cloud.pigx.data.center.controller.request.catalog.CatalogQry"
            resultMap="catalog">
        select * from t_catalog
        where 1=1
        <if test="catalogName != null and catalogName != ''">
            and catalog_name like concat('%',#{catalogName},'%')
        </if>
        <if test="catalogNo != null and catalogNo != ''">
            and catalog_no like concat('%',#{catalogNo},'%')
        </if>
        <if test="catalogState != null">
            and catalog_state = #{catalogState}
        </if>
        <if test="catalogType!=null">
            and catalog_type = #{catalogType}
        </if>
        order by catalog_no asc
    </select>



    <select id="recursiveList" parameterType="com.pig4cloud.pigx.data.center.controller.request.catalog.CatalogQry"
            resultMap="catalog">
        with RECURSIVE result_data as (
        select id,catalog_no,catalog_name,catalog_state,catalog_level,catalog_parent_no
        from t_catalog
        <where>
            <choose>
                <when test="catalogNo != null and catalogNo != ''">
                    and catalog_no = #{catalogNo}
                </when>
                <otherwise>
                    and catalog_parent_no = '00'
                </otherwise>
            </choose>
            <if test="catalogState != null">
                and catalog_state = #{catalogState}
            </if>
            <if test="catalogType != null">
                and catalog_type = #{catalogType}
            </if>
        </where>
        union all
        select ct.id,ct.catalog_no,ct.catalog_name,ct.catalog_state,ct.catalog_level,ct.catalog_parent_no from
        t_catalog ct join result_data rd on ct.catalog_parent_no = rd.catalog_no
        <where>
            <if test="catalogState != null">
                and ct.catalog_state = #{catalogState}
            </if>
        </where>
        ) select * from result_data order by catalog_no asc;
    </select>


    <select id="listByParentNo" resultMap="catalog">
        select * from t_catalog
        where catalog_parent_no
        in(
        <foreach collection="parentNos" index="index" item="item" separator=",">
            #{item}
        </foreach>
        )
        and catalog_type = #{catalogType}
        <if test="catalogState!=null">
            and catalog_state = #{catalogState}
        </if>
        order by catalog_no asc
    </select>

    <select id="selectMaxNoByParentNo" resultType="java.lang.String">
        select max(catalog_no) from t_catalog
        where catalog_parent_no = #{catalogParentNo}
    </select>

    <select id="selectByNo" resultMap="catalog">
        select * from t_catalog
        where catalog_no = #{no}
    </select>

    <select id="recursiveListReverse" parameterType="com.pig4cloud.pigx.data.center.controller.request.catalog.CatalogQry"
            resultMap="catalog">
        with RECURSIVE result_data as (
        select id,catalog_no,"catalog_name",catalog_state,catalog_level,catalog_parent_no
        from t_catalog
        <where>
            catalog_no = #{catalogNo}
            <if test="catalogState != null">
                and catalog_state = #{catalogState}
            </if>
        </where>
        union all
        select ct.id,ct.catalog_no,ct."catalog_name",ct.catalog_state,ct.catalog_level,ct.catalog_parent_no from
        t_catalog ct join result_data rd on ct.catalog_no = rd.catalog_parent_no
        <where>
            <if test="catalogState != null">
                and ct.catalog_state = #{catalogState}
            </if>
        </where>
        ) select * from result_data order by catalog_no asc;
    </select>







    <delete id="deleteByParentNo">
        delete from t_catalog where catalog_parent_no = #{catalogParentNo}
    </delete>

    <delete id="deleteByParentNos">
        delete from t_catalog where catalog_parent_no in(
        <foreach collection="parentNos" index="index" item="item" separator=",">
            #{item}
        </foreach>
        )
    </delete>
    <delete id="deleteByIds">
        delete from t_catalog where id in(
        <foreach collection="ids" index="index" item="item" separator=",">
            #{item}
        </foreach>
        )
    </delete>

    <select id="listByCatalogNos" resultType="com.pig4cloud.pigx.data.center.entity.Catalog">
        select * from t_catalog
        where catalog_no in(
        <foreach collection="catalogNos" index="index" item="item" separator=",">
            #{item}
        </foreach>
        )
    </select>

    <select id="getFirstCatalog" resultType="com.pig4cloud.pigx.data.center.entity.Catalog">
        select * from t_catalog
        where catalog_parent_no ='00' and catalog_type = #{catalogType}
    </select>

    <select id="getByNameAndParentNo" resultType="java.lang.Integer">
        select 1 from t_catalog where catalog_name = #{catalogName} and catalog_parent_no = #{catalogParentNo}
        <if test="catalogType!=null">
            and catalog_type = #{catalogType}
        </if>
        limit 1
    </select>

    <update id="updateStateByParentNo">
        update t_catalog set catalog_state = #{catalogState} where catalog_parent_no = #{parentCatalogNo}
    </update>

    <update id="updateStateByParentNos">
        update t_catalog set catalog_state = #{catalogState} where catalog_parent_no in(
        <foreach collection="parentCatalogNos" index="index" item="item" separator=",">
            #{item}
        </foreach>
        )
    </update>
    <update id="updateStateByIds">
        update t_catalog set catalog_state = #{catalogState} where id in(
        <foreach collection="ids" index="index" item="item" separator=",">
            #{item}
        </foreach>
        )
    </update>
    <update id="updateStateById">
        update t_catalog set catalog_state = #{catalogState} where id = #{id}
    </update>
</mapper>
