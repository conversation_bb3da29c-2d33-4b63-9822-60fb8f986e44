<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pig4cloud.pigx.data.center.mapper.OperateLogMapper">



    <update id="resumeLog" parameterType="java.util.Map">
        UPDATE operate_log SET del_flag = '0' WHERE del_flag = '1' and
        <foreach item="item" collection="list" separator=" or " open="(" close=")">
          table_name = #{item.tableName} and business_id IN
          <foreach item="item2"   collection="item.resourceIds" separator="," open="(" close=") ">
              #{item2}
          </foreach>
        </foreach>

    </update>
</mapper>