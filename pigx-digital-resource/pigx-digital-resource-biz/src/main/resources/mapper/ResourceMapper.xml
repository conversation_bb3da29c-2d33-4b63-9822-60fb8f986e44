<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pig4cloud.pigx.data.center.mapper.ResourceMapper">


    <!-- 插入数据 -->
    <insert id="insertData" parameterType="com.pig4cloud.pigx.data.center.entity.resource.ModelDataEntity"
            useGeneratedKeys="true" keyProperty="id">
        INSERT INTO ${tableName}
        <foreach collection="datas.keys" item="key" open="(" close=")" separator=",">
            ${key}
        </foreach>
        VALUES
        <foreach collection="datas.values" item="value" open="(" close=")" separator=",">
            #{value}
        </foreach>
    </insert>


    <!-- 批量插入数据 -->
    <insert id="insertBatchData" useGeneratedKeys="true" keyProperty="list.id">
        INSERT INTO ${tableName}
        <foreach collection="list.get(0).keys" item="key" open="(" close=")" separator=",">
            ${key}
        </foreach>
        VALUES
        <foreach collection="list" item="datas" separator=",">
            <foreach collection="datas.values" item="value" open="(" close=")" separator=",">
                #{value}
            </foreach>
        </foreach>
    </insert>


    <!-- 更新数据 -->
    <update id="updateData" parameterType="com.pig4cloud.pigx.data.center.entity.resource.ModelDataEntity">
        UPDATE ${tableName} SET
        <foreach collection="datas" index="key" item="value" separator=",">
            ${key} = #{value}
        </foreach>
        WHERE id = #{id}
    </update>

    <!-- 删除数据 -->
    <delete id="deleteData">
        DELETE
        FROM ${tableName}
        WHERE id in
        <foreach collection="ids" item="id" separator="," open="(" close=")">
            #{id}
        </foreach>
    </delete>

    <!-- 逻辑删除数据 -->
    <delete id="logicDeleteData">
        UPDATE ${tableName} set del_flag = '1' WHERE id in
        <foreach collection="ids" item="id" separator="," open="(" close=")">
            #{id}
        </foreach>
    </delete>

    <!-- 清空表数据 -->
    <delete id="clearTable" parameterType="java.lang.String">
        DELETE
        FROM ${tableName} ${ew.customSqlSegment}
    </delete>


    <!-- 删除数据 -->
    <delete id="deleteList" parameterType="com.pig4cloud.pigx.data.center.entity.resource.ModelDataDeleteEntity">
        DELETE
        FROM ${tableName}
        WHERE id in (
        <foreach collection="ids" item="id" separator=",">
            #{id}
        </foreach>
        )
    </delete>

    <select id="getPageModelDatas" resultType="java.util.Map">
        SELECT ${ew.SqlSelect}
        FROM ${tableName} ${ew.customSqlSegment}
    </select>
    <select id="getData" resultType="java.util.Map">
        SELECT *
        FROM ${tableName}
        WHERE id = #{id}
    </select>

    <select id="getQueryWrapperData" resultType="java.util.Map">
        SELECT ${ew.SqlSelect}
        FROM ${tableName} ${ew.customSqlSegment}
        limit 1
    </select>


    <select id="getListModelDatas" resultType="java.util.Map">
        SELECT ${ew.SqlSelect}
        FROM ${tableName} ${ew.customSqlSegment}
    </select>

	<select id="getListModelDatasCount" >
		SELECT count(1)
		FROM ${tableName} ${ew.customSqlSegment}
	</select>

    <select id="selectExist" resultType="java.lang.Boolean">
        SELECT EXISTS (SELECT 1
                       FROM information_schema.tables
                       WHERE table_schema = 'public'
                         AND table_name = #{tableName});
    </select>
</mapper>
