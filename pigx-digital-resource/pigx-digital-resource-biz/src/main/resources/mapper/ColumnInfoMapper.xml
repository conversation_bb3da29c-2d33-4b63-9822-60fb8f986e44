<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.pig4cloud.pigx.data.center.mapper.ColumnInfoMapper">

    <resultMap id="columnInfoMap" type="com.pig4cloud.pigx.data.center.entity.ColumnInfo">
        <id property="id" column="id"/>
        <result property="cnName" column="cn_name"/>
        <result property="remark" column="comment"/>
        <result property="dataLength" column="data_length"/>
        <result property="dataType" column="data_type"/>
        <result property="name" column="name"/>
        <result property="securityId" column="security_id"/>
        <result property="shareType" column="share_type"/>
        <result property="tableInfoId" column="table_info_id"/>
        <result property="createTime" column="create_time"/>
        <result property="tableName" column="table_name"/>
        <result property="columnRule" column="column_rule"/>
        <result property="publishState" column="publish_state"/>
        <result property="columnCustomeRule" column="column_custome_rule"/>
    </resultMap>
    <select id="pageColumnInfo" resultMap="columnInfoMap"
            parameterType="com.pig4cloud.pigx.data.center.controller.request.column.ColumnInfoQry">
        select * from column_info
        where use_state !=2
        <if test="tableInfoId!=null">
            and table_info_id=#{tableInfoId}
        </if>
        <if test="useState!=null">
            and use_state=#{useState}
        </if>
        <if test="name !=null and name !=''">
            and cn_name like concat('%',#{name},'%')
        </if>
        <if test="columnName !=null and columnName !=''">
            and name like concat('%',#{columnName},'%')
        </if>
        <if test="tableName !=null and tableName !=''">
            and table_name = #{tableName}
        </if>
        <if test="groupId!=null">
            and group_id=#{groupId}
        </if>
        <if test="showFlag!=null">
            and show_flag!=0
        </if>
        order by id desc
    </select>
    <select id="checkName" resultType="java.lang.Integer">
        select 1 from column_info where name=#{name} and group_id=#{groupId} limit 1
    </select>
    <select id="listColumnsByTableIdAndTableName"
            resultType="com.pig4cloud.pigx.data.center.entity.ColumnInfo">
        select * from column_info where table_name=#{tableName} and use_state !=2
    </select>
    <select id="checkColumnName" resultType="java.lang.Integer">
        select 1 from column_info where name=#{name} and table_name=#{tableName} and use_state !=2 limit 1
    </select>

    <select id="getColumnInfo" resultMap="columnInfoMap">
        select * from column_info where name=#{name} and table_name=#{tableName} and use_state !=2 limit 1
    </select>
</mapper>