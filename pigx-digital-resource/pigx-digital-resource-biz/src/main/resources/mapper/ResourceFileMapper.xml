<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pig4cloud.pigx.data.center.mapper.ResourceFileMapper">


    <select id="selectByMaterialId" resultType="com.pig4cloud.pigx.data.center.entity.ResourceFile">
        select rf.* from material_file mf left join resource_file rf on mf.file_id = rf.id where mf.material_id = #{materialId}   limit 1
    </select>


    <select id="selectByMaterialIdList" resultType="com.pig4cloud.pigx.data.center.entity.ResourceFile">
        select rf.main_file,rf.object_key ,rf.total_size ,rf.url, mf.material_id as id, rf.download_url from material_file mf left join resource_file rf on mf.file_id = rf.id where mf.material_id in
        <foreach collection="list" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
    </select>
    <select id="listArchiveFiles" resultType="com.pig4cloud.pigx.data.center.entity.ResourceFile"
            parameterType="com.pig4cloud.pigx.data.center.controller.request.archive.ArchiveQry">
        select rf.* from resource_file rf inner join archive_data_file adf on rf.id = adf.resource_file_id
        where adf.archive_id = #{id} and adf.archive_table_name = #{tableName}
        order by rf.sort_num desc
    </select>
</mapper>