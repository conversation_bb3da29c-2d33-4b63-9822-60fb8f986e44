<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.pig4cloud.pigx.data.center.mapper.ArchiveTypeMapper">

    <resultMap id="archiveType" type="com.pig4cloud.pigx.data.center.entity.ArchiveType">
        <id property="id" column="id"/>
        <result property="typeName" column="type_name"/>
        <result property="typeCode" column="type_code"/>
        <result property="typeTemplate" column="type_template"/>
        <result property="typeRemark" column="type_remark"/>
        <result property="typeParentId" column="type_parent_id"/>
        <result property="tableName" column="table_name"/>
        <result property="delFlag" column="del_flag"/>
    </resultMap>
    <select id="selectChildIds" resultType="java.lang.Integer">
        select id from archive_type where type_parent_id in(
        <foreach collection="parentIds" item="parentId" separator=",">
            #{parentId}
        </foreach>)
    </select>
    <select id="selectChilds" resultMap="archiveType">
        select * from archive_type where type_parent_id in(
        <foreach collection="parentIds" item="parentId" separator=",">
            #{parentId}
        </foreach>)
    </select>
    <select id="checkUniqueCode" resultMap="archiveType">
        select * from archive_type where type_code = #{typeCode} order by id desc limit 1
    </select>
    <select id="getByTableName" resultMap="archiveType">
        select * from archive_type where table_name = #{tableName} limit 1
    </select>
    <select id="getByTableNames" resultType="com.pig4cloud.pigx.data.center.entity.ArchiveType">
        select * from archive_type where table_name in(
        <foreach collection="tableNames" item="tableName" separator=",">
            #{tableName}
        </foreach>)
    </select>

</mapper>
