<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.pig4cloud.pigx.data.center.mapper.ColumnGroupMapper">

    <resultMap id="catalog" type="com.pig4cloud.pigx.data.center.entity.ColumnGroup">
        <id property="id" column="id"/>
        <result property="groupName" column="group_name"/>
        <result property="metaDataId" column="meta_data_id"/>
        <result property="groupSort" column="group_sort"/>
        <result property="groupType" column="group_type"/>
    </resultMap>
    <select id="listColumnGroup" resultType="com.pig4cloud.pigx.data.center.entity.ColumnGroup"
            parameterType="com.pig4cloud.pigx.data.center.controller.request.column.ColumnGroupQry">
        select * from t_column_group where 1=1
        <if test="metaDataId != null">
            and meta_data_id = #{metaDataId}
        </if>
        order by group_sort asc
    </select>

</mapper>
