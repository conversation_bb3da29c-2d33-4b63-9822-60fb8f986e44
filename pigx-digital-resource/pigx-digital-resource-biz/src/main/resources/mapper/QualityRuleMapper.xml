<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.pig4cloud.pigx.data.center.mapper.QualityRuleMapper">

  <resultMap id="qualityRuleMap" type="com.pig4cloud.pigx.data.center.entity.QualityRule">
        <id property="id" column="id"/>
        <result property="comment" column="comment"/>
        <result property="dataType" column="data_type"/>
        <result property="name" column="name"/>
        <result property="owner" column="owner"/>
        <result property="ruleType" column="rule_type"/>
        <result property="sqlTemplate" column="sql_template"/>
        <result property="createTime" column="create_time"/>
  </resultMap>
</mapper>