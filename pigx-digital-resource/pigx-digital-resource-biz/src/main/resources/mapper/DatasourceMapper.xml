<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.pig4cloud.pigx.data.center.mapper.DatasourceMapper">

  <resultMap id="datasourceMap" type="com.pig4cloud.pigx.data.center.entity.Datasource">
        <id property="id" column="id"/>
        <result property="dbType" column="db_type"/>
        <result property="description" column="description"/>
        <result property="extraInfo" column="extra_info"/>
        <result property="hosts" column="hosts"/>
        <result property="name" column="name"/>
        <result property="owner" column="owner"/>
        <result property="password" column="password"/>
        <result property="rdbms" column="rdbms"/>
        <result property="userName" column="user_name"/>
        <result property="createTime" column="create_time"/>
  </resultMap>
</mapper>