<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.pig4cloud.pigx.data.center.mapper.MetaDataGenerateMapper">

    <resultMap id="metaDataGenerate" type="com.pig4cloud.pigx.data.center.entity.MetaDataGenerate">
        <id property="id" column="id"/>
        <result property="catalogNo" column="catalog_no"/>
        <result property="countor" column="countor"/>
    </resultMap>
    <select id="getMaxCountor" resultType="java.lang.Integer">
        select max(countor) from t_meta_data_table_generate where catalog_no = #{catalogNo}
    </select>

</mapper>
