<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pig4cloud.pigx.data.center.mapper.LeaMaterialMapper">

    <resultMap type="com.pig4cloud.pigx.data.center.entity.LeaMaterial" id="LeaMaterialResult">
        <result property="id" column="id"/>
        <result property="name" column="assets_name"/>
        <result property="type" column="type"/>
        <result property="typeStr" column="type_str"/>
        <result property="description" column="description"/>
        <result property="cover" column="cover"/>
        <result property="securityId" column="security_id"/>
        <result property="assetsNum" column="assets_num"/>
        <result property="lng" column="lng"/>
        <result property="lat" column="lat"/>
        <result property="format" column="format"/>
        <result property="labels" column="labels"/>
        <result property="delFlag" column="del_flag"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="remark" column="remark"/>
        <result property="dataSource" column="data_source"/>
    </resultMap>

    <sql id="selectMaterialVo">
        select distinct md.id,
                        md.assets_name,
                        md.type,
                        md.description,
                        md.cover,
                        md.lng,
                        md.lat,
                        md.format,
                        md.del_flag,
                        md.create_by,
                        md.create_time,
                        md.update_by,
                        md.update_time,
                        md.remark,
                        md.labels,
                        md.security_id,
                        md.assets_num
        from lea_material md
    </sql>

    <select id="selectLeaMaterialList" parameterType="com.pig4cloud.pigx.data.center.controller.request.leamaterial.MaterialPageQuery" resultMap="LeaMaterialResult">
        <include refid="selectMaterialVo"/>
        <where>
            md.del_flag = '0' and lng is not null and lat is not null and md.in_recycle = 0
            <if test="type != null and type != ''">and md.type = #{type}</if>
            <if test="fileName != null and fileName != ''">and md.assets_name like concat('%', #{fileName}, '%')</if>
            <if test="assetsNum != null and assetsNum != ''">and md.assets_num like concat('%', #{assetsNum}, '%')</if>
            <if test="createBy != null and createBy != ''">and ((md.owner_id is null and md.create_by like concat('%',
                #{createBy}, '%')) or md.owner_name like concat('%', #{createBy}, '%') )
            </if>
            <if test="format != null and format != ''">and md.format = #{format}</if>

            <if test="beginTime != null and beginTime != ''"><!-- 开始时间检索 -->
                AND DATE_FORMAT(md.create_time,'yyyy-MM-dd')::date &gt;= to_date(#{beginTime},'yyyy-MM-dd')

            </if>
            <if test="endTime != null and endTime != ''"><!-- 结束时间检索 -->
                AND DATE_FORMAT(md.create_time,'yyyy-MM-dd')::date &lt;= to_date(#{endTime},'yyyy-MM-dd')
            </if>


            <if test="geometry !=null and geometry !=''">
                and st_intersects (ST_GeomFromText( 'POINT(' || md.lng || ' ' || md.lat || ')' , 4326) , ST_GeomFromText
                (#{geometry}, 4326 ) )
            </if>
        </where>
        <choose>
            <when test="sortName != null and sortName != ''">
                order by ${sortName}
            </when>
            <otherwise>
                order by md.create_time desc, md.name asc, md.type asc
            </otherwise>
        </choose>
    </select>

    <select id="selectPointList" resultType="com.pig4cloud.pigx.data.center.entity.dto.MaterialPointDTO">
        select m.id, m.assets_name as name, m.type, m.description, m.lng, m.lat, m.format, m.create_by,
        m.create_time
        from lea_material m
        where del_flag = '0' and lng is not null and lat is not null
        <choose>

            <when test="'oneself'.toString() == accessType">and ((owner_id is null and create_user_id = #{createUserId})
                or owner_id = #{createUserId})
            </when>
            <when test="createUserId == null">and public_status = '1'</when>
            <otherwise>
                and ( public_status = '1'
                or (public_status = '3' and (owner_id = #{createUserId} or create_user_id = #{createUserId}) )

                <if test="groupUserIdList != null and groupUserIdList.size() > 0">
                    or (public_status = '2' and ( owner_id in
                    <foreach collection="groupUserIdList" item="item" open="( " separator="," close=")">
                        #{item}
                    </foreach>
                    or create_user_id in
                    <foreach collection="groupUserIdList" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    ))
                </if>
                )
            </otherwise>
        </choose>
    </select>

    <select id="selectListByCombinationId" resultMap="LeaMaterialResult">
        <include refid="selectMaterialVo"/>
        where md.del_flag = '0' and md.id in (select material_id from lea_combination_material where combination_id =
        #{id})
        order by md.create_time desc, md.assets_name asc, md.type asc
    </select>

	<select id="pageNoRelation" resultType="com.pig4cloud.pigx.data.center.vo.leamaterial.LeaMaterialPageVO">
		select md.id,
			   md.assets_name as name,
			   md.type,
			   md.description,
			   md.cover,
			   md.lng,
			   md.lat,
			   md.format,
			   md.del_flag,
			   md.create_by,
			   md.create_time,
			   md.update_by,
			   md.update_time,
			   md.remark,
			   md.labels,
			   md.security_id,
			   md.assets_num,
			   c.name as catalogNo,
			   c.size as totalSize
		from data_material b
				 left join lea_material md on md.id = b.material_id
				 left join data_assets c on b.data_id = c.business_id
				 where 1=1 and b.del_flag = '0'
		<if test="queryParams.isWarehouse == 0">
			and md.warehouse_id is null
		</if>
        <if test="queryParams.assetsNum != null and queryParams.assetsNum != ''">
            and md.assets_num like concat('%', #{queryParams.assetsNum}, '%')
        </if>
        <if test="queryParams.fileName != null and queryParams.fileName != ''">
            and md.assets_name like concat('%', #{queryParams.fileName}, '%')
        </if>
        <if test="queryParams.createUserId != null">
            and md.create_by = #{queryParams.createUserId}
        </if>
		<if test="queryParams.isWarehouse == 1 and queryParams.warehouseId != null">
			and md.warehouse_id is not null
			<if test="queryParams.warehouseId != 0">
				and md.warehouse_id = #{queryParams.warehouseId}
			</if>
		</if>

	</select>



    <select id="checkNameUnique" parameterType="String" resultMap="LeaMaterialResult">
        select id, assets_name as name
        from lea_material
        where assets_name = #{name}
          and del_flag = '0'
        limit 1
    </select>

    <select id="batchCheckNameUnique" parameterType="String" resultType="String">
        select assets_name as name from lea_material where del_flag = '0' and assets_name in
        <foreach item="name" collection="list" open="(" separator="," close=")">
            #{name}
        </foreach>
    </select>

    <select id="selectLeaMaterialById" resultMap="LeaMaterialResult">
        <include refid="selectMaterialVo"/>
        where md.id = #{id} and md.del_flag = '0'
    </select>

	<select id="selectLeaMaterialByIdIsDelete" resultMap="LeaMaterialResult">
		<include refid="selectMaterialVo"/>
		where md.id = #{id} and md.del_flag = '1'
	</select>

    <select id="selectCountByFolderId" resultType="long">
        select count(1)
        from lea_material
        where del_flag = '0'
          and folder_id = #{folderId}
    </select>

    <select id="selectIdsByFolderId" parameterType="Long" resultType="Long">
        select id
        from lea_material
        where del_flag = '0'
          and folder_id = #{id}
    </select>

    <select id="selectDistinctObjectKeyByIds" resultType="String">
        select object_key from lea_material where del_flag = '0' and object_key in (
        select object_key from lea_material where del_flag = '0' and object_key is not null and id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
        ) group by object_key having count(object_key) = 1
    </select>

    <select id="selectByFileIdentifier" parameterType="String" resultMap="LeaMaterialResult">
        select id,
               folder_id,
               assets_name name,
               lng,
               lat,
               file_identifier,
               object_key,
               url,
               main_file
        from lea_material
        where del_flag = '0'
          and object_key is not null
          and file_identifier = #{code}
    </select>

    <select id="selectIdsByFolderIds" parameterType="Long" resultType="Long">
        select id from lea_material where del_flag = '0' and folder_id in
        <foreach item="id" collection="list" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <insert id="insertLeaMaterial" parameterType="com.pig4cloud.pigx.data.center.entity.LeaMaterial">
        insert into lea_material
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="folderId != null">folder_id,</if>
            <if test="name != null">assets_name,</if>
            <if test="maker != null">maker,</if>
            <if test="type != null">type,</if>
            <if test="description != null">description,</if>
            <if test="cover != null">cover,</if>
            <if test="publicStatus != null">public_status,</if>
            <if test="commentStatus != null">comment_status,</if>
            <if test="downloadStatus != null">download_status,</if>
            <if test="shareStatus != null">share_status,</if>
            <if test="quoteStatus != null">quote_status,</if>
            <if test="lng != null">lng,</if>
            <if test="lat != null">lat,</if>
            <if test="name != null">name,</if>
            <if test="format != null">format,</if>
            <if test="mainFile != null">main_file,</if>
            <if test="totalSize != null">total_size,</if>
            <if test="fileIdentifier != null">file_identifier,</if>
            <if test="bucketName != null">bucket_name,</if>
            <if test="objectKey != null">object_key,</if>
            <if test="chunkSize != null">chunk_size,</if>
            <if test="chunkNum != null">chunk_num,</if>
            <if test="uploadId != null">upload_id,</if>
            <if test="properties != null">properties,</if>
            <if test="url != null">url,</if>
            <if test="publicationServiceFlag != null">publication_service_flag,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="createUserId != null">create_user_id,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="remark != null">remark,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="folderId != null">#{folderId},</if>
            <if test="name != null">#{name},</if>
            <if test="maker != null">#{maker},</if>
            <if test="type != null">#{type},</if>
            <if test="description != null">#{description},</if>
            <if test="cover != null">#{cover},</if>
            <if test="publicStatus != null">#{publicStatus},</if>
            <if test="commentStatus != null">#{commentStatus},</if>
            <if test="downloadStatus != null">#{downloadStatus},</if>
            <if test="shareStatus != null">#{shareStatus},</if>
            <if test="quoteStatus != null">#{quoteStatus},</if>
            <if test="lng != null">#{lng},</if>
            <if test="lat != null">#{lat},</if>
            <if test="fileName != null">#{fileName},</if>
            <if test="format != null">#{format},</if>
            <if test="mainFile != null">#{mainFile},</if>
            <if test="totalSize != null">#{totalSize},</if>
            <if test="fileIdentifier != null">#{fileIdentifier},</if>
            <if test="bucketName != null">#{bucketName},</if>
            <if test="objectKey != null">#{objectKey},</if>
            <if test="chunkSize != null">#{chunkSize},</if>
            <if test="chunkNum != null">#{chunkNum},</if>
            <if test="uploadId != null">#{uploadId},</if>
            <if test="properties != null">#{properties},</if>
            <if test="url != null">#{url},</if>
            <if test="publicationServiceFlag != null">#{publicationServiceFlag},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createUserId != null">#{createUserId},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="remark != null">#{remark},</if>
        </trim>
    </insert>

    <update id="updateLeaMaterial" parameterType="com.pig4cloud.pigx.data.center.entity.LeaMaterial">
        update lea_material
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null">assets_name = #{name},</if>
            <if test="type != null">type = #{type},</if>
            <if test="description != null">description = #{description},</if>
            <if test="folderId != null">folder_id = #{folderId},</if>
            <if test="cover != null">cover = #{cover},</if>

            <if test="lng != null">lng = #{lng},</if>
            <if test="lat != null">lat = #{lat},</if>
            <if test="format != null">format = #{format},</if>
            <if test="mainFile != null">main_file = #{mainFile},</if>
            <if test="totalSize != null">total_size = #{totalSize},</if>
            <if test="fileIdentifier != null">file_identifier = #{fileIdentifier},</if>
            <if test="bucketName != null">bucket_name = #{bucketName},</if>
            <if test="objectKey != null">object_key = #{objectKey},</if>
            <if test="chunkSize != null">chunk_size = #{chunkSize},</if>
            <if test="chunkNum != null">chunk_num = #{chunkNum},</if>
            <if test="uploadId != null">upload_id = #{uploadId},</if>
            <if test="properties != null">properties = #{properties},</if>
            <if test="url != null">url = #{url},</if>
            <if test="publicationServiceFlag != null">publication_service_flag = #{publicationServiceFlag},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
            update_time = current_timestamp
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteLeaMaterialById" parameterType="Long">
        delete
        from lea_material
        where id = #{id}
    </delete>

    <delete id="deleteLeaMaterialByIds" parameterType="Long">
        delete from lea_material where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="useDisk" resultType="Long">
        select sum(total_size) from lea_material where
        del_flag = '0' and object_key is not null
        <if test="userId != null ">and ( (owner_id is null and create_user_id = #{userId}) or owner_id = #{userId} )
        </if>
    </select>
    <select id="totalDisk" resultType="Long">
        select total_disk_capacity
        from sys_user
        where user_id = #{userId}
    </select>

	<update id="updateDelFlag">
		update lea_material set update_time = current_timestamp,del_flag='0' where del_flag = '1' and id in
		<foreach item="id" collection="list" open="(" separator="," close=")">
			#{id}
		</foreach>
	</update>
</mapper>