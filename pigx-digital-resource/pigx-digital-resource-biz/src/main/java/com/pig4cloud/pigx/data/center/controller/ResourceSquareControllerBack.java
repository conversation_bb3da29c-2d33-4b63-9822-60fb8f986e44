package com.pig4cloud.pigx.data.center.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.pig4cloud.pigx.common.core.util.R;
import com.pig4cloud.pigx.data.center.common.annocations.OpeLog;
import com.pig4cloud.pigx.data.center.common.enums.OperateEnum;
import com.pig4cloud.pigx.data.center.controller.request.resource.DownloadRequest;
import com.pig4cloud.pigx.data.center.entity.ColumnGroup;
import com.pig4cloud.pigx.data.center.entity.query.ModelDataQuery;
import com.pig4cloud.pigx.data.center.service.ResourceSquareServiceBack;
import com.pig4cloud.pigx.data.center.vo.leamaterial.LeaMaterialPageVO;
import com.pig4cloud.pigx.data.center.vo.resource.ColumnInfoVO;
import com.pig4cloud.pigx.data.center.vo.resourcesquare.SquarePageVO;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * 资源广场
 */
@RestController
@RequestMapping("/resource/square-old")
@RequiredArgsConstructor
public class ResourceSquareControllerBack {

	private final ResourceSquareServiceBack resourceSquareService;

	/**
	 * 获取资源分页数据
	 *
	 * @return
	 */
	@GetMapping("/page")
	public R<Page<SquarePageVO>> page(ModelDataQuery query) {
		return resourceSquareService.selectPage(query);
	}


	/**
	 * 查看资源详情
	 *
	 * @param tabName
	 * @param resourceId
	 * @return
	 */
	@GetMapping("/info")
	@OpeLog(operate = OperateEnum.INFO, value = "查看资源详情", tableName = "#arg0", businessId = "#arg1")
	public R<Map<String, Object>> info(@RequestParam("tabName") String tabName, @RequestParam("resourceId") Long resourceId) {
		return resourceSquareService.info(tabName, resourceId);
	}

	/**
	 * 按照组分段获取信息
	 *
	 * @param groupId
	 * @return
	 */
	@GetMapping("/shareLink/{groupId}")
	public R<List<ColumnInfoVO>> segment(@PathVariable("groupId") Integer groupId,
										 @RequestParam(value = "token", required = false) String token,
										 @RequestParam(value = "tabName", required = false) String tabName,
										 @RequestParam(value = "resourceId", required = false) Long resourceId) {
		return resourceSquareService.segment(groupId, token, tabName, resourceId);
	}

	/**
	 * 通过分享的链接访问
	 *
	 * @param token
	 * @return
	 */
	@GetMapping("/shareLink")
	public R<Map<String, Object>> shareLink(@RequestParam(value = "token", required = false) String token,
											@RequestParam(value = "tabName", required = false) String tabName,
											@RequestParam(value = "resourceId", required = false) Long resourceId) {
		return resourceSquareService.shareLink(token, tabName, resourceId);
	}


	/**
	 * 获取所有的菜单组
	 *
	 * @param token
	 * @return
	 */
	@GetMapping("/shareLink/listColumnGroup")
	public R<List<ColumnGroup>> listColumnGroup(@RequestParam(value = "token", required = false) String token,
												@RequestParam(value = "tabName", required = false) String tabName) {
		return resourceSquareService.listColumnGroup(token, tabName);
	}

	/**
	 * 获取默认字段以及值
	 *
	 * @param token
	 * @return
	 */
	@GetMapping("/shareLink/defaultColumn/value")
	@OpeLog(operate = OperateEnum.INFO, tableName = "#arg1", businessId = "#arg2", expression = "'查看资源详情分组:[' + #arg2 +']的数据'")
	public R<Map<String, Object>> defaultColumn(@RequestParam(value = "token", required = false) String token,
												@RequestParam(value = "tabName", required = false) String tabName,
												@RequestParam(value = "resourceId", required = false) Long resourceId) {
		return resourceSquareService.defaultColumn(token, tabName, resourceId);
	}


	/**
	 * 资源广场-文件管理查询
	 * @param token
	 * @param tabName
	 * @param searchValue
	 * @param resourceId
	 * @param size
	 * @param current
	 * @return
	 */
	@GetMapping("/material/page")
	public R<Page<LeaMaterialPageVO>> materialPage(@RequestParam(value = "token", required = false) String token,
												   @RequestParam(value = "tabName", required = false) String tabName,
												   @RequestParam(value = "searchValue", required = false) String searchValue,
												   @RequestParam(value = "resourceId", required = false) Long resourceId,
												   @RequestParam(value = "size", required = false, defaultValue = "10") Integer size,
												   @RequestParam(value = "current", required = false, defaultValue = "1") Integer current) {
		return resourceSquareService.materialPage(null, tabName, resourceId, searchValue, size, current);
	}

	/**
	 * 资源广场-文件下载
	 * @param request
	 * @param response
	 * @throws IOException
	 */
	@GetMapping("/download")
	public void download(@Validated DownloadRequest request, HttpServletResponse response) throws IOException {
		resourceSquareService.download(request, response);
	}




}
