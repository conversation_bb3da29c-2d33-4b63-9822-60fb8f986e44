package com.pig4cloud.pigx.data.center.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.pig4cloud.pigx.common.core.enums.DataAssetsTypeEnum;
import com.pig4cloud.pigx.common.core.util.R;
import com.pig4cloud.pigx.data.center.common.annocations.OpeLog;
import com.pig4cloud.pigx.data.center.common.enums.OperateEnum;
import com.pig4cloud.pigx.data.center.controller.request.archiveborrow.*;
import com.pig4cloud.pigx.data.center.service.ArchiveBorrowApplyService;
import com.pig4cloud.pigx.data.center.vo.archiveborrow.*;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 档案利用
 */
@RestController
@RequestMapping("/archive/borrow")
@RequiredArgsConstructor
public class ArchiveBorrowApplyController {

    private final ArchiveBorrowApplyService archiveBorrowApplyService;


    /**
     * 申请借阅
     *
     * @param request
     * @return
     */
    @PostMapping("/add")
    public R add(@Validated @RequestBody BorrowApplyAddRequest request) {
        return archiveBorrowApplyService.add(request);
    }

    /**
     * 电子档案申请撤销
     *
     * @param request
     * @return
     */
    @PostMapping("/cancel")
    public R cancel(@Validated @RequestBody BorrowCancelRequest request) {
        return archiveBorrowApplyService.cancel(request);
    }


    /**
     * 我的借阅 -- 分页
     *
     * @param request
     * @return
     */
    @GetMapping("/page")
    public R<Page<BorrowApplyPageVO>> borrowPage(BorrowApplyPageRequest request) {
        return archiveBorrowApplyService.page(request);
    }


    /**
     * 我的借阅 -- 查看详情
     *
     * @param id
     * @return
     */
    @GetMapping("/info")
    public R<ArchiveBorrowInfoVO> info(@RequestParam("id") Long id) {
        return archiveBorrowApplyService.info(id);
    }


    /**
     * 查看档案详情
     *
     * @param id
     * @return
     */
    @GetMapping("/archive/info")
    @OpeLog(operate = OperateEnum.INFO, tableName = "#arg0", value = "查看档案详情", businessId = "#arg1", dataType = DataAssetsTypeEnum.RCHIVE_DATA)
    public R<ArchiveBorrowInfoVO> archiveInfo(@RequestParam("tableName") String tableName, @RequestParam("id") Long id) {
        return archiveBorrowApplyService.archiveInfo(tableName, id);
    }


    /**
     * 借阅审批 -- 待审批
     *
     * @return
     */

    @GetMapping("/auditing/page")
    public R<Page<BorrowAuditingPageVO>> auditingPage(BorrowAuditingPageRequest request) {
        return archiveBorrowApplyService.auditingPage(request);
    }


    /**
     * 借阅审批 -- 审批历史
     *
     * @return
     */

    @GetMapping("/audited/page")
    public R<Page<BorrowAuditedPageVO>> auditedPage(@Validated BorrowAuditedPageRequest request) {
        return archiveBorrowApplyService.auditedPage(request);
    }


    /**
     * 借阅审批 -- 审批
     *
     * @param auditRequest
     * @return
     */
    @PostMapping("/audit")
    public R audit(@RequestBody BorrowApplyAuditRequest auditRequest) {
        return archiveBorrowApplyService.audit(auditRequest);
    }

    /**
     * 档案归还
     *
     * @param request
     * @return
     */
    @GetMapping("/return/page")
    public R<Page<BorrowReturnPageVO>> returnPage(BorrowApplyReturnRequest request) {
        return archiveBorrowApplyService.returnPage(request);
    }

    /**
     * 审核列表
     *
     * @param request
     * @return
     */
    @GetMapping("/all/page")
    public R<Page<BorrowAuditAllPageVO>> allPage(BorrowApplyReturnRequest request) {
        return archiveBorrowApplyService.allPage(request);
    }

}
