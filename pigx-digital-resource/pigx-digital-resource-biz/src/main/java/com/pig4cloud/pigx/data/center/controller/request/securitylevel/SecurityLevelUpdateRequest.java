package com.pig4cloud.pigx.data.center.controller.request.securitylevel;

import lombok.Data;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Positive;
import java.io.Serializable;

@Data
public class SecurityLevelUpdateRequest implements Serializable {

    @NotNull
    private Long id;


    @NotEmpty
    private String securityName;

    @NotNull
    @Positive
    private Integer sortOrder;

    @NotNull
    @Range(max = 1)
    private Integer enabled;

    private String remark;
}
