package com.pig4cloud.pigx.data.center;

import com.pig4cloud.pigx.common.feign.annotation.EnablePigxFeignClients;
import com.pig4cloud.pigx.common.security.annotation.EnablePigxResourceServer;
import com.pig4cloud.pigx.common.swagger.annotation.EnableOpenApi;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;

@EnableOpenApi("datacenter")
@EnablePigxFeignClients
@EnablePigxResourceServer
@EnableDiscoveryClient
@EnableScheduling
@EnableAsync
@SpringBootApplication(scanBasePackages = {"com.pig4cloud.pigx.data.center", "com.daspatial.gis.config", "com.daspatial.gis.common.utils", "com.daspatial.gis.common.config"})
public class DigitalResourceApplication {

    private static final Logger LOGGER= LoggerFactory.getLogger(DigitalResourceApplication.class);

    public static void main(String[] args) {
        LOGGER.info("DataCenterApplication is starting...........");
        SpringApplication.run(DigitalResourceApplication.class, args);
    }
}
