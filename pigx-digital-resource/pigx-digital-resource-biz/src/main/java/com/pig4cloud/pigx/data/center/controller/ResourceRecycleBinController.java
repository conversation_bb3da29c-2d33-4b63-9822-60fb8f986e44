package com.pig4cloud.pigx.data.center.controller;


import com.pig4cloud.pigx.common.core.util.R;
import com.pig4cloud.pigx.data.center.controller.request.recyclebin.RecycleBinOperateRequest;
import com.pig4cloud.pigx.data.center.controller.request.recyclebin.RecycleBinPageRequest;
import com.pig4cloud.pigx.data.center.service.ResourceRecycleBinService;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotNull;

@RestController
@RequestMapping("/recycle/bin")
@RequiredArgsConstructor
public class ResourceRecycleBinController {

    @NonNull
    private final ResourceRecycleBinService resourceRecycleBinService;

    /**
     * 查询分页数据
     *
     * @param request
     * @return
     */
    @GetMapping("/page")
    //@PreAuthorize("@pms.hasPermission('recycle_bin_view')")
    public R page(RecycleBinPageRequest request) {
        return resourceRecycleBinService.selectPage(request);
    }

    /**
     * 批量操作回收站(1:删除  2：还原)
     *
     * @param request
     * @return
     */
    @PostMapping("/operate")
    //@PreAuthorize("@pms.hasPermission('recycle_bin_update')")
    public R operate(@Validated @RequestBody RecycleBinOperateRequest request) {
        return resourceRecycleBinService.operate(request);
    }

    @GetMapping("/info")
    //@PreAuthorize("@pms.hasPermission('recycle_bin_view')")
    public R info(@RequestParam("id") @NotNull Long id){
        return resourceRecycleBinService.info(id);
    }
}
