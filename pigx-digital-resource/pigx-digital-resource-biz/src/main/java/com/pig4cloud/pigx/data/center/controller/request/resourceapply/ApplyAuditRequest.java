package com.pig4cloud.pigx.data.center.controller.request.resourceapply;

import com.pig4cloud.pigx.flow.task.dto.TaskParamDto;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

@Data
public class ApplyAuditRequest extends TaskParamDto {

    @NotNull
    private Long resourceApplyId;

    @NotEmpty
    private String taskId;

    private String flowId;
}
