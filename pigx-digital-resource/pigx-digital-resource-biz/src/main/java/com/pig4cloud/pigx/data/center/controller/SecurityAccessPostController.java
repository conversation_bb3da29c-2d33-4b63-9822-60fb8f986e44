package com.pig4cloud.pigx.data.center.controller;

import com.pig4cloud.pigx.common.core.util.R;
import com.pig4cloud.pigx.data.center.service.SecurityAccessPostService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/security/post")
@RequiredArgsConstructor
public class SecurityAccessPostController {

    private final SecurityAccessPostService securityAccessPostService;


    /**
     * 根据userId 删除权限列表配置的用户
     *
     * @param ids
     * @return
     */
    @DeleteMapping("")
    public R removeByUserIds(@RequestBody List<Long> ids) {
        return securityAccessPostService.removeByUserIds(ids);
    }
}
