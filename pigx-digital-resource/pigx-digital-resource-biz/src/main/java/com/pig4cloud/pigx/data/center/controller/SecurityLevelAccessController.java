package com.pig4cloud.pigx.data.center.controller;

import com.pig4cloud.pigx.common.core.util.R;
import com.pig4cloud.pigx.data.center.controller.request.securityaccess.SecurityAccessAddRequest;
import com.pig4cloud.pigx.data.center.controller.request.securityaccess.SecurityAccessDeleteRequest;
import com.pig4cloud.pigx.data.center.controller.request.securityaccess.SecurityAccessPageRequest;
import com.pig4cloud.pigx.data.center.controller.request.securityaccess.SecurityAccessUpdateRequest;
import com.pig4cloud.pigx.data.center.service.SecurityLevelAccessService;
import com.pig4cloud.pigx.data.center.vo.securityaccess.SecurityAccessInfoVO;
import lombok.RequiredArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/security/access")
@RequiredArgsConstructor
public class SecurityLevelAccessController {

    private final SecurityLevelAccessService securityLevelAccessService;

    /**
     * 增加权限配置
     *
     * @param request
     * @return
     */

    @PostMapping("/add")
    //@PreAuthorize("@pms.hasPermission('sys_security_access_add')")
    public R add(@Validated @RequestBody SecurityAccessAddRequest request) {
        return securityLevelAccessService.add(request);
    }

    /***
     * 权限配置分页
     * @param request
     * @return
     */
    @GetMapping("/page")
    //@PreAuthorize("@pms.hasPermission('sys_security_access_view')")
    public R page(@Validated SecurityAccessPageRequest request) {
        return securityLevelAccessService.page(request);
    }


    @PostMapping("/update")
    //@PreAuthorize("@pms.hasPermission('sys_security_access_update')")
    public R update(@Validated @RequestBody SecurityAccessUpdateRequest request) {
        return securityLevelAccessService.update(request);
    }

    /**
     * 权限配置详情
     *
     * @param id
     * @return
     */

    @GetMapping("/info")
    //@PreAuthorize("@pms.hasPermission('sys_security_access_view')")
    public R<SecurityAccessInfoVO> info(@RequestParam Long id) {
        return securityLevelAccessService.info(id);
    }


    /**
     * 删除配置详情
     *
     * @param request
     * @return
     */
    @PostMapping("/delete")
    //@PreAuthorize("@pms.hasPermission('sys_security_access_delete')")
    public R delete(@Validated @RequestBody SecurityAccessDeleteRequest request) {
        return securityLevelAccessService.delete(request.getId());
    }


}
