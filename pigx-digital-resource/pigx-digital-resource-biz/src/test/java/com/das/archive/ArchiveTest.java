package com.das.archive;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.das.BaseTest;
import com.pig4cloud.pigx.common.core.util.CommonBuilder;
import com.pig4cloud.pigx.data.center.entity.ArchiveType;
import com.pig4cloud.pigx.data.center.service.ArchiveTypeService;
import org.apache.commons.lang3.RandomStringUtils;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

public class ArchiveTest extends BaseTest {
    @Autowired
    private ArchiveTypeService archiveTypeService;

    @Test
    public void addType() {
        ArchiveType archiveType = CommonBuilder.of(ArchiveType.class)
                .with(ArchiveType::setTypeName, "档案" + System.currentTimeMillis() + "")
                .with(ArchiveType::setTypeCode, RandomStringUtils.randomAlphanumeric(8))
                .with(ArchiveType::setTypeTemplate, 1)
                .with(ArchiveType::setTypeParentId, 3L)
                .with(ArchiveType::setTypeRemark, System.currentTimeMillis() + "")
                .build();
        archiveTypeService.addArchiveType(archiveType);
    }

    @Test
    public void delType() {
        ArchiveType archiveType = CommonBuilder.of(ArchiveType.class)
                .with(ArchiveType::setId, 2l)
                .build();
        archiveTypeService.delArchiveType(archiveType);
    }

    @Test
    public void listType() {
        ArchiveType archiveType = CommonBuilder.of(ArchiveType.class)
                .with(ArchiveType::setId, 2l)
                .build();
        System.err.println(JSON.toJSONString(archiveTypeService.listArchiveType(), SerializerFeature.PrettyFormat));
    }
}
