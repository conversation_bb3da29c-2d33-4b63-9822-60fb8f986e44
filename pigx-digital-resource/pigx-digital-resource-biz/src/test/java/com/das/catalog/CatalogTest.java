package com.das.catalog;

import com.alibaba.druid.pool.DruidDataSource;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.das.BaseTest;
import com.pig4cloud.pigx.common.core.util.CommonBuilder;
import com.pig4cloud.pigx.common.core.util.ListBuilder;
import com.pig4cloud.pigx.common.core.util.R;
import com.pig4cloud.pigx.data.center.common.components.GenCodeComponent;
import com.pig4cloud.pigx.data.center.common.util.CatalogUtil;
import com.pig4cloud.pigx.data.center.controller.CatalogController;
import com.pig4cloud.pigx.data.center.controller.request.catalog.CatalogQry;
import com.pig4cloud.pigx.data.center.entity.Catalog;
import com.pig4cloud.pigx.data.center.entity.DataAssets;
import com.pig4cloud.pigx.data.center.entity.query.Condition;
import com.pig4cloud.pigx.data.center.entity.query.ModelDataQuery;
import com.pig4cloud.pigx.data.center.es.Es8ClientUtil;
import com.pig4cloud.pigx.data.center.es.EsClientUtil;
import com.pig4cloud.pigx.data.center.es.request.DataAssetsToEs;
import com.pig4cloud.pigx.data.center.es.request.Location;
import com.pig4cloud.pigx.data.center.es.response.CountResult;
import com.pig4cloud.pigx.data.center.es.service.ResourceEsService;
import com.pig4cloud.pigx.data.center.service.DataAssetsService;
import org.apache.ibatis.session.SqlSession;
import org.elasticsearch.action.get.GetResponse;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.aggregations.Aggregation;
import org.elasticsearch.search.aggregations.AggregationBuilders;
import org.elasticsearch.search.aggregations.Aggregations;
import org.elasticsearch.search.aggregations.bucket.terms.ParsedStringTerms;
import org.elasticsearch.search.aggregations.bucket.terms.Terms;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.junit.jupiter.api.Test;
import org.mybatis.spring.SqlSessionTemplate;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.Stream;

public class CatalogTest extends BaseTest {
    @Autowired
    private CatalogController catalogController;

    @Autowired
    private DataAssetsService dataAssetsService;

    @Autowired
    private GenCodeComponent genCodeComponent;

    @Autowired
    private SqlSessionTemplate sqlSessionTemplate;

    @Autowired
    private SqlSession sqlSession;

    @Autowired
    private DruidDataSource druidDataSource;

    @Autowired
    private CatalogUtil catalogUtil;


    @Test
    public void list() {
/*
    http://127.0.0.1:9999/datacenter/catalog/list
{
            "catalogNo": "0",
                "catalogName": "文",
                "catalogSate": 1
        }*/
        CatalogQry catalogQry = CommonBuilder.of(CatalogQry.class)
//                .with(CatalogQry::setCatalogState, 1)
//                .with(CatalogQry::setCatalogNo, "01")
//                .with(CatalogQry::setCatalogName, "玉真宫")
                .with(CatalogQry::setNeedLevel4, 1)
                .build();
        R list = catalogController.list(catalogQry);
        System.err.println(JSON.toJSONString(list, SerializerFeature.PrettyFormat));
    }

    @Test
    public void add() {
/*
    http://127.0.0.1:9999/datacenter/catalog/add
{
	"catalogLevel":2,
	"catalogName":"太极拳武功秘籍",
	"catalogParentNo":"03",
	"catalogRemark":"太极拳武功秘籍",
	"catalogState":1
}*/
        Catalog catalogQry = CommonBuilder.of(Catalog.class)
                .with(Catalog::setCatalogCreator, "wdstest")
                .with(Catalog::setCatalogName, "test1")
                .with(Catalog::setCatalogState, 1)
                .with(Catalog::setCatalogLevel, 2)
                .with(Catalog::setCatalogCreateTm, new Date())
                .with(Catalog::setCatalogRemark, "test1")
                .with(Catalog::setCatalogParentNo, "06")
                .with(Catalog::setCatalogSort, 1)
                .build();
        System.err.println(JSON.toJSONString(catalogQry, SerializerFeature.PrettyFormat));
        R list = catalogController.add(catalogQry);
        System.err.println(JSON.toJSONString(list, SerializerFeature.PrettyFormat));
    }
    /*http://127.0.0.1:9999/datacenter/catalog/edit
    *{
	"catalogLevel":2,
	"catalogName":"太极拳武功秘籍1",
	"catalogParentNo":"04",
	"catalogRemark":"太极拳武功秘籍1",
	"catalogState":1,
    "id":15
}
    * */

    /*
    *http://127.0.0.1:9999/datacenter/catalog/remove
    * {
	"catalogCreateTm":1722391298555,
	"catalogCreator":"wdstest",
	"catalogLevel":3,
	"catalogName":"test22",
	"catalogParentNo":"06002",
	"catalogRemark":"test22",
	"catalogSort":1,
	"catalogState":1,
    "id":16
}
    * */

    @Test
    public void addDataAssets() {
        DataAssets dataAssets = CommonBuilder.of(DataAssets.class)
                .with(DataAssets::setCatalogNo, "02001005")
                .with(DataAssets::setAssetsDepId, 1l)
                .with(DataAssets::setAssetsUserId, 1l)
                .with(DataAssets::setName, "玉真宫")
                .with(DataAssets::setCreateTime, LocalDateTime.now())
                .with(DataAssets::setAssetsType, 1)
                .with(DataAssets::setSecurityLevel, 1L)
                .build();
        System.err.println(JSON.toJSONString(dataAssets, SerializerFeature.PrettyFormat));
//        dataAssetsService.add(dataAssets);
    }

    @Test
    public void genCode() {
        String prefix = "001";
        int length = 6;
        String code = genCodeComponent.generateCode(prefix, length);
        System.err.println(code);
        System.err.println(99 * 999 * 999);
    }

    /**
     * http://127.0.0.1:9999/datacenter/dataAssets/addMetaData
     * {
     * "assetsType": 1,
     * "catalogNo": "02001005",
     * "name": "玉真宫",
     * "reportMode":1,
     * "reportFrequency":1,
     * "adminTel":"13335333653",
     * "reportRemark":"哈喽",
     * "remark":"资产备注",
     * "useState":1,
     * "adminName":"张三"
     * }
     */
    @Test
    public void getParentCatalog() {
        Catalog parentCatalog = catalogUtil.getParentCatalog("02001003");
        System.err.println(JSON.toJSONString(parentCatalog, SerializerFeature.PrettyFormat));
        Map<String, Catalog> parentCatalog1 = catalogUtil.getParentCatalog(Stream.of("02001003", "02001001", "02001", "02").collect(Collectors.toList()));
//        Map<String, Catalog> parentCatalog1 = catalogUtil.getParentCatalog(Stream.of("02001","02001").collect(Collectors.toList()));
        System.err.println(JSON.toJSONString(parentCatalog1, SerializerFeature.PrettyFormat));
    }


    @Autowired
    private EsClientUtil esClientUtil;

    @Autowired
    private Es8ClientUtil es8ClientUtil;

    @Test
    public void writeEs() {
        String json = "{  \n" +
                "  \"assets_name\": \"太极拳\",  \n" +
                "  \"assets_tag\": \"健身, 武术\",  \n" +
                "  \"assets_remark\": \"太极拳是一种内家拳，强调以柔克刚。\",  \n" +
                "  \"assets_location\": \"武当山, 湖北省\",  \n" +
                "  \"department\": \"武术协会\",  \n" +
                "  \"security\": \"低\",  \n" +
                "  \"assets_code\": \"WDS003\",  \n" +
                "  \"create_user\": \"王五\",  \n" +
                "  \"create_time\": \"2024-08-06 12:00:00\",  \n" +
                "  \"table_name\": \"assets_table\",  \n" +
                "  \"third_catalog_no\": \"01\",  \n" +
                "  \"second_catalog_no\": \"01001\",  \n" +
                "  \"first_catalog_no\": \"01001003\",  \n" +
                "  \"business_id\": 125,  \n" +
                "  \"assets_type\": 3,  \n" +
                "  \"location\": {  \n" +
                "    \"type\": \"point\",  \n" +
                "    \"coordinates\": [110.2, 30.2]   \n" +
                "  }  \n" +
                "}";
        DataAssetsToEs dataAssetsToEs = JSON.parseObject(json, DataAssetsToEs.class);
        esClientUtil.writeData(ListBuilder.<DataAssetsToEs>ofList().add(dataAssetsToEs).build(), ListBuilder.<String>ofList().add("WDS003").build(), "");
    }

    @Test
    public void deleteEs() {
        esClientUtil.deleteData("WDS003", "");
    }

    @Test
    public void searchEsById() {
        GetResponse getResponse = esClientUtil.searchDataById("", "5");
        System.err.println(JSON.toJSONString(getResponse.getSource(), SerializerFeature.PrettyFormat));
    }

    @Test
    public void searchEs() {
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        BoolQueryBuilder should = QueryBuilders.boolQuery().should(QueryBuilders.matchQuery("assets_name", "武当")).should(QueryBuilders.matchQuery("assets_remark", "九阳"));
        searchSourceBuilder.query(should);
        System.err.println(searchSourceBuilder.toString());
        SearchResponse getResponse = esClientUtil.searchData(searchSourceBuilder, "wudang_dataassets");
        System.err.println(JSON.toJSONString(getResponse.getHits().getHits(), SerializerFeature.PrettyFormat));
    }

    @Test
    public void searchCount() {
        List<CountResult> countResults = ListBuilder.<CountResult>ofList().build();
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        searchSourceBuilder.size(0);
        searchSourceBuilder.aggregation(AggregationBuilders.terms("first_catalog_no").field("first_catalog_no"));
        SearchResponse searchResponse = esClientUtil.searchData(searchSourceBuilder, "wudang_dataassets");
        Aggregations aggregations = searchResponse.getAggregations();
        Aggregation firstCatalogNo = aggregations.get("first_catalog_no");
        List<? extends Terms.Bucket> buckets = ((ParsedStringTerms) firstCatalogNo).getBuckets();
        buckets.forEach(bucket -> {
            CountResult countResult = new CountResult();
            countResult.setItemNo(bucket.getKeyAsString());
            countResult.setItemCount(new BigDecimal(bucket.getDocCount()));
            countResults.add(countResult);
        });
        System.err.println(JSON.toJSONString(countResults, SerializerFeature.PrettyFormat));
    }

    @Autowired
    private ResourceEsService resourceEsService;

    @Test
    public void advancedSearch() {
        ModelDataQuery modelDataQuery = CommonBuilder.of(ModelDataQuery.class)
                .with(ModelDataQuery::setCatalogNos, Arrays.asList("01001001", "01001002", "01001003"))
                .with(ModelDataQuery::setCreateTimeEnd, "2024-08-07 12:00:00")
                .with(ModelDataQuery::setCreateTimeStart, "2024-08-06 12:00:00")
                .with(ModelDataQuery::setSortField, "create_time")
                .with(ModelDataQuery::setSortType, "desc")
                .build();
        List<Condition> conditions = ListBuilder.<Condition>ofList()
                .add(CommonBuilder.of(Condition.class)
                        .with(Condition::setColumn, "assets_name")
                        .with(Condition::setExact, "exact")
                        .with(Condition::setRelation, "should")
                        .with(Condition::setValue, "太极拳")
                        .build())
                .build();
        modelDataQuery.setConditions(conditions);
        System.err.println(JSON.toJSONString(modelDataQuery, SerializerFeature.PrettyFormat));
        List<? extends DataAssetsToEs> dataAssetsToEs = resourceEsService.advancedSearch(modelDataQuery);
        System.err.println(JSON.toJSONString(dataAssetsToEs, SerializerFeature.PrettyFormat));
    }

    @Test
    public void keywordSearch() {
        ModelDataQuery modelDataQuery = CommonBuilder.of(ModelDataQuery.class)
//                .with(ModelDataQuery::setKeyword, "十堰 九阳")
//                .with(ModelDataQuery::setTableName, "t_meta_data_02001002_16")
//                .with(ModelDataQuery::setSortField, "create_time")
//                .with(ModelDataQuery::setSortType, "desc")
                .build();
        List<DataAssetsToEs> dataAssetsToEs = resourceEsService.keywordSearch(modelDataQuery);
        System.err.println(JSON.toJSONString(dataAssetsToEs, SerializerFeature.PrettyFormat));
    }

    @Test
    public void deleteData() {
        esClientUtil.deleteData(ListBuilder.<String>ofList().add("WDS003").add("1").build(), "");
    }

    @Test
    public void updateData() {
        List<String> ids = ListBuilder.<String>ofList()
                .add("1826552155318595584")
                .build();
        List<DataAssetsToEs> dataAssetsToEsList = ListBuilder.<DataAssetsToEs>ofList().build();
        DataAssetsToEs dataAssetsToEs = new DataAssetsToEs();
        dataAssetsToEs.setAssets_type(2);
        dataAssetsToEs.setAssets_remark("测试一下部分数据更新1");
//        dataAssetsToEs.setFirst_catalog_no("02");
//        dataAssetsToEs.setFirst_catalog_name("空间系");
//        dataAssetsToEs.setSecond_catalog_name("宫");
//        dataAssetsToEs.setThird_catalog_name("玉真宫");
//        dataAssetsToEs.setAssets_cover("https://b1-q.mafengwo.net/s14/M00/A4/31/wKgE2l0GaI6ADFTWAF6MScJgzeo819.jpg?imageMogr2%2Fthumbnail%2F%21690x370r%2Fgravity%2FCenter%2Fcrop%2F%21690x370%2Fquality%2F100");
        dataAssetsToEsList.add(dataAssetsToEs);
        Location location = CommonBuilder.of(Location.class)
                .with(Location::setType, "point")
                .with(Location::setCoordinates, new Object[]{110.1, 30.1})
                .build();
        dataAssetsToEs.setLocation(location);
        es8ClientUtil.updateData("wudang_dataassets", ids, dataAssetsToEsList);
    }

    @Test
    public void countByCatalog() {
        ModelDataQuery modelDataQuery = CommonBuilder.of(ModelDataQuery.class)
                .with(ModelDataQuery::setKeyword, "十堰 九阳 管理局")
//                .with(ModelDataQuery::setSortField, "create_time")
//                .with(ModelDataQuery::setSortType, "desc")
                .build();
        List<CountResult> countResults = dataAssetsService.countByCatalog(null);
        System.err.println(JSON.toJSONString(countResults, SerializerFeature.PrettyFormat));
    }

    @Test
    public void getNextNumber() {
        System.err.println(catalogUtil.getNextNumber(99, 2));
    }

    @Test
    public void getCatalogVo() {
        System.err.println(JSON.toJSONString(catalogUtil.getCatalogVo("12004001")));
        System.err.println(JSON.toJSONString(catalogUtil.getCatalogVo("12004")));
        System.err.println(JSON.toJSONString(catalogUtil.getCatalogVo("12")));
    }
}
