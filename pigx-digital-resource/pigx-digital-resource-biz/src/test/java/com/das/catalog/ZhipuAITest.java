package com.das.catalog;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.pig4cloud.pigx.common.core.util.CommonBuilder;
import com.pig4cloud.pigx.common.core.util.HttpRequestUtil;
import com.pig4cloud.pigx.common.core.util.ListBuilder;
import org.junit.jupiter.api.Test;

import java.io.BufferedWriter;
import java.io.File;
import java.io.FileWriter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class ZhipuAITest {
    @Test
    public void embeddings() throws Exception {
        String url = "https://open.bigmodel.cn/api/paas/v4/embeddings";
        Map<String, String> header = new HashMap<>();
        header.put("Content-Type", "application/json");
        header.put("Authorization", "Bearer 4df471b3522e29be7c35892ff96823ed.ZglBOjAuli6BvhMb");
        EmbeddingParams embeddingParams = CommonBuilder.of(EmbeddingParams.class)
                .with(EmbeddingParams::setInput, ListBuilder.<String>ofList().add("刘玄德").build())
                .build();
        String result = HttpRequestUtil.doPost(url, header, JSON.toJSONString(embeddingParams));
        JSONObject jsonObject = JSON.parseObject(result);
        JSONArray jsonArray = jsonObject.getJSONArray("data");
        JSONObject embeddingObj = (JSONObject) jsonArray.get(0);
        JSONArray embedding = embeddingObj.getJSONArray("embedding");
        List<String> build = ListBuilder.<String>ofList().build();
        for (Object o : embedding) {
            build.add(o.toString());
        }
        String join = String.join(",", build);
        File file = new File("D:\\pytho_code\\my_python\\my_python\\com\\das\\wds\\text_vector.txt");
        FileWriter fileReader = new FileWriter(file);
        BufferedWriter bufferedReader = new BufferedWriter(fileReader);
        bufferedReader.write(join);
        bufferedReader.close();
        fileReader.close();
        System.out.println(join);
    }
}
