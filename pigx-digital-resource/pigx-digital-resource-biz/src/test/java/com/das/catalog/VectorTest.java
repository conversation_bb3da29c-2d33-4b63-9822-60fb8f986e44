package com.das.catalog;

import cn.hutool.core.util.IdUtil;
import co.elastic.clients.elasticsearch._types.KnnQuery;
import co.elastic.clients.elasticsearch._types.query_dsl.BoolQuery;
import co.elastic.clients.elasticsearch.core.SearchRequest;
import co.elastic.clients.elasticsearch.core.SearchResponse;
import co.elastic.clients.elasticsearch.core.search.Hit;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.das.BaseTest;
import com.pig4cloud.pigx.common.core.util.CommonBuilder;
import com.pig4cloud.pigx.common.core.util.HttpRequestUtil;
import com.pig4cloud.pigx.common.core.util.ListBuilder;
import com.pig4cloud.pigx.data.center.es.Es8ClientUtil;
import com.pig4cloud.pigx.data.center.es.EsClientUtil;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileReader;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

public class VectorTest extends BaseTest {
    @Autowired
    private Es8ClientUtil es8ClientUtil;

    @Autowired
    private EsClientUtil esClientUtil;


    @Test
    public void vectorWrite() throws Exception {
        List<Float> embedding = textToVector();
        MyVector myVector = CommonBuilder.of(MyVector.class)
                .with(MyVector::setDepartment, "刘玄德")
                .with(MyVector::setEmbedding, embedding)
                .build();
        esClientUtil.writeData(myVector, IdUtil.getSnowflakeNextId() + "", "my_embedding");
    }

    @Test
    public void vectorSearch() throws Exception {
        // 设置查询向量
        BoolQuery.Builder boolQueryBuilder = new BoolQuery.Builder();
        List<Float> queryVector = textToVector();
        System.err.println(queryVector);
        KnnQuery.Builder builder = new KnnQuery.Builder();
        builder.field("embedding")
                .numCandidates(100)
                .k(10)
//                .similarity(0.3f)
                .queryVector(queryVector);
        // 创建搜索请求
        SearchRequest searchRequest = new SearchRequest.Builder()
                .index("my_embedding")
                .knn(builder.build())
                .size(10)
                .build();

        // 执行搜索请求
        SearchResponse<Product> searchResponse = es8ClientUtil.searchData(searchRequest, "my_embedding", Product.class);
        for (Hit<Product> hit : searchResponse.hits().hits()) {
            System.err.println("相关度评分：" + hit.score());
            System.err.println(hit.source());
            System.err.println();
        }
    }

    /**
     * 要写入es 的数据
     *
     * @return
     */
    private static List<Float> textToVector() throws Exception {
        List<Float> collect = new LinkedList<>();
        File file = new File("D:\\pytho_code\\my_python\\my_python\\com\\das\\wds\\text_vector.txt");
        FileReader fileReader = new FileReader(file);
        BufferedReader bufferedReader = new BufferedReader(fileReader);
        String line = bufferedReader.readLine();
        for (String s : line.split(",")) {
            collect.add(Double.valueOf(s).floatValue());
        }
        return collect;
    }

    private static List<Float> textToVectorV1() throws Exception {
        Float[] floats = new Float[]{-2.784125328063965f, 0.6198880672454834f, -0.4522164762020111f, 0.14846168458461761f, 0.89305579662323f, -0.12764215469360352f, 5.114452362060547f, 4.676681041717529f, 2.89993953704834f, 1.1343190670013428f, 0.007840842008590698f, -0.851931631565094f, 3.061464786529541f, -0.46498578786849976f, -2.7685155868530273f, -1.515987753868103f, 1.2696925401687622f, -2.6934525966644287f, 2.397451639175415f, 0.7981650829315186f, -2.543679714202881f, 0.5659337043762207f, -1.7699650526046753f, -14.634157180786133f, 0.03451782464981079f, 2.459653377532959f, -0.17066505551338196f, 3.2191944122314453f, -1.3204904794692993f, 0.3471211791038513f, 3.2803261280059814f, -0.5297337770462036f, -0.8253229856491089f, -0.3800891935825348f, -0.48508256673812866f, 3.013261318206787f, -0.631726861000061f, 1.3476518392562866f, 2.7243759632110596f, 1.5756648778915405f, -0.38168007135391235f, 4.252195835113525f, -1.192656397819519f, -2.958214282989502f, 1.3358235359191895f, 4.155867576599121f, 0.766834020614624f, 1.5023937225341797f, 1.779341220855713f, 0.8823608756065369f, -1.4348152875900269f, 1.1270332336425781f, 0.30278703570365906f, 0.4110267758369446f, -4.715835094451904f, -0.5333535671234131f, 3.111215591430664f, -3.3331196308135986f, -0.9663132429122925f, -2.933950901031494f, 0.020017147064208984f, -3.7569968700408936f, 0.6471880078315735f, 2.6966047286987305f, 0.06623274087905884f, -0.9559423327445984f, -1.29336416721344f, 1.452143907546997f, -0.7919099926948547f, -1.366524338722229f, 0.9401290416717529f, 0.2960189878940582f, 3.7866909503936768f, -1.4421626329421997f, -2.018310546875f, -0.5200734734535217f, -1.6489672660827637f, -0.11523476243019104f, -1.0051671266555786f, 3.5921895503997803f, -2.1847119331359863f, -0.23798495531082153f, 2.568898916244507f, -0.4048722982406616f, -3.5885088443756104f, -2.422581195831299f, 0.7787788510322571f, 1.947385311126709f, 0.8343749642372131f, -1.1539576053619385f, 1.3012382984161377f, -0.8644518256187439f, 0.28074002265930176f, 1.9023678302764893f, 0.2604207396507263f, 0.2720797061920166f, 0.9650946855545044f, -3.9619832038879395f, -0.9128645658493042f, 0.6161010265350342f, -2.7743356227874756f, -1.3730201721191406f, 1.5562779903411865f, -0.11140508949756622f, -1.1475111246109009f, 0.5431616306304932f, 1.7960151433944702f, -1.186156153678894f, 2.5512630939483643f, -5.633957862854004f, 1.6042547225952148f, -0.9146787524223328f, -0.19753307104110718f, -0.1111917495727539f, -0.06187295913696289f, 1.834763526916504f, 0.2514837384223938f, -2.939953088760376f, 2.932206869125366f, -0.74979567527771f, 1.045654535293579f, -0.6504287123680115f, -0.3264228105545044f, -2.013915777206421f, 1.6211869716644287f, 2.9542412757873535f, -0.986249566078186f, -0.8921096920967102f, 1.1322267055511475f, 6.516430854797363f, -1.992967128753662f, 1.0342597961425781f, 1.7357676029205322f, 3.6449315547943115f, 0.1839422583580017f, 2.5957562923431396f, 0.6510290503501892f, 1.1284159421920776f, 1.1845548152923584f, 1.7684142589569092f, -0.7747746706008911f, -1.2390216588974f, -1.0392512083053589f, 0.24754762649536133f, -0.27193206548690796f, 2.9968302249908447f, -1.3986709117889404f, 2.160842180252075f, 0.4909643530845642f, -0.8720671534538269f, 3.5171797275543213f, 1.0994634628295898f, 2.2784523963928223f, -4.974119186401367f, -3.9942424297332764f, 0.46889543533325195f, 1.3547348976135254f, 1.244451880455017f, -2.121568202972412f, 0.014256924390792847f, -1.7594387531280518f, 1.1040034294128418f, 3.4485812187194824f, -0.2805802524089813f, 3.9421229362487793f, -2.1259140968322754f, -0.5542454719543457f, -1.2474273443222046f, 0.7871631383895874f, 1.9843955039978027f, -0.162886381149292f, 2.536787986755371f, 2.602811336517334f, -0.34618258476257324f, -1.1532362699508667f, -2.6908938884735107f, -1.2231115102767944f, -1.507706880569458f, 2.2486462593078613f, 1.487426996231079f, 1.8984606266021729f, -1.5042866468429565f, -0.24444159865379333f, -1.1870967149734497f, -0.8929467797279358f, 1.8800578117370605f, 2.320688247680664f, -1.3159511089324951f, -2.1614274978637695f, 1.7981605529785156f, -4.371057033538818f, 5.614361763000488f, 3.295764923095703f, -0.9991065859794617f, -1.0990890264511108f, 4.491870403289795f, 2.855727195739746f, 0.598918616771698f, 0.6276106238365173f, -2.1060824394226074f, -2.465768575668335f, 1.101854920387268f, 4.6603288650512695f, -4.483386993408203f, 1.124478816986084f, 1.8932315111160278f, 1.7672151327133179f, -0.4926653802394867f, -3.0841522216796875f, 1.3660517930984497f, 2.5597827434539795f, -0.6093034744262695f, 0.18646842241287231f, 1.5924540758132935f, 2.2248497009277344f, -1.2052165269851685f, -2.518739938735962f, 2.3919661045074463f, 1.4822590351104736f, 1.9221770763397217f, -0.05269676446914673f, 0.8339297771453857f, -2.213576555252075f, 0.7970703840255737f, 0.6942501068115234f, 1.8140331506729126f, -0.4116077721118927f, -0.3371692895889282f, -0.800264835357666f, -1.2190290689468384f, -2.1293461322784424f, 4.9611968994140625f, -2.6044793128967285f, 1.1166925430297852f, -1.7511427402496338f, -0.42874813079833984f, 5.714578151702881f, -1.1105519533157349f, -1.5269136428833008f, -1.023573398590088f, -0.9482229948043823f, 3.0156328678131104f, 1.249877691268921f, -4.764025688171387f, -1.7855156660079956f, 3.1057019233703613f, -2.264350652694702f, 0.3639141917228699f, 2.6616063117980957f, 2.667954444885254f, 1.4054887294769287f, -0.6811886429786682f, 0.02023249864578247f, 19.853696823120117f, 0.8743518590927124f, -2.7278482913970947f, -0.2023843228816986f, 0.7800917625427246f, 0.3562588691711426f, -0.5141937136650085f, -0.6772556304931641f, -0.8627282381057739f, 1.5146665573120117f, 1.3835749626159668f, 0.20941144227981567f, -1.08354651927948f, -2.008899211883545f, 0.8224647641181946f, 2.3026349544525146f, 0.45307105779647827f, 0.5920318365097046f, -2.266580104827881f, 0.2517585754394531f, 4.276089191436768f, 1.567301869392395f, -3.0217156410217285f, -0.6716257333755493f, -0.568105936050415f, 0.2645252048969269f, 0.1112879142165184f, 1.1091243028640747f, -2.0843417644500732f, 0.8062893748283386f, 1.6540610790252686f, 3.179804801940918f, -4.252564907073975f, 1.592269778251648f, -1.0853943824768066f, -0.6092492341995239f, -5.653341293334961f, 6.750948905944824f, -0.3691643476486206f, 1.2049260139465332f, -0.22595110535621643f, 0.27363085746765137f, -1.6263926029205322f, -1.492529273033142f, 3.4107842445373535f, 2.9068150520324707f, 1.483298659324646f, -0.20398789644241333f, -1.3863627910614014f, 0.5727734565734863f, 1.422856092453003f, -0.47551053762435913f, -1.1929516792297363f, 1.569796085357666f, -0.3153003454208374f, 1.5708190202713013f, 1.3637479543685913f, -1.6550308465957642f, -3.858189105987549f, 2.930312156677246f, 3.347740411758423f, 1.6159851551055908f, -0.7435441017150879f, -0.4746076464653015f, -4.577447414398193f, -1.807010531425476f, -0.4114718735218048f, 0.3065222501754761f, 0.7286447286605835f, -1.6191861629486084f, 4.8356733322143555f, 1.457455039024353f, -0.18054038286209106f, 1.584722876548767f, 0.7591007351875305f, 1.8232303857803345f, -1.445116639137268f, -1.589285969734192f, -1.7272489070892334f, -1.6182098388671875f, -3.767765760421753f, 0.38722383975982666f, 0.35366469621658325f, 1.8888466358184814f, -0.007229462265968323f, 0.5463577508926392f, -1.921703815460205f, -1.6133781671524048f, 2.127376079559326f, -2.9213273525238037f, -0.3864566683769226f, -2.8325772285461426f, 2.071859121322632f, -1.1286654472351074f, -2.6137938499450684f, 0.10829095542430878f, -3.1573781967163086f, 1.3202576637268066f, 0.3726547658443451f, 2.298490285873413f, -3.1125054359436035f, -2.4102566242218018f, 2.257237434387207f, 1.442204236984253f, -3.1733500957489014f, -1.510560154914856f, -0.8404843807220459f, 0.8403432369232178f, -1.5451123714447021f, 1.3790302276611328f, -1.0224992036819458f, -3.1415693759918213f, 1.3807653188705444f, -0.7008122205734253f, -2.9780685901641846f, -1.5296630859375f, 2.4080703258514404f, 2.386842727661133f, -0.17428997159004211f, 0.6481250524520874f, -0.9162288308143616f, 0.4366701543331146f, 4.19731330871582f, 1.605747938156128f, -0.6951616406440735f, 0.1205836609005928f, 1.0469284057617188f, 2.272698163986206f, 0.46840977668762207f, 1.1536426544189453f, -1.6275279521942139f, 0.26924389600753784f, 1.1865358352661133f, -1.7222280502319336f, 0.7015148997306824f, 1.3966314792633057f, 0.2739979326725006f, -2.37056040763855f, -0.09665556252002716f, 0.7649811506271362f, 3.8376357555389404f, 1.4438506364822388f, 0.492174357175827f, -0.23136119544506073f, -2.8275928497314453f, 0.1561315357685089f, -0.24394702911376953f, -2.157914161682129f, 3.3776869773864746f, 2.2098097801208496f, -1.4213857650756836f, -0.20261043310165405f, 1.1146382093429565f, -0.5121452808380127f, -2.2500240802764893f, -0.9171690940856934f, -3.5263166427612305f, -2.6755921840667725f, -0.2732749879360199f, -3.448622226715088f, -0.2709808349609375f, 1.2047334909439087f, -1.0753663778305054f, 3.019542932510376f, 1.7479830980300903f, 2.364929676055908f, 0.3108106255531311f, 0.9392415285110474f, 2.0164616107940674f, -0.24535031616687775f, 1.3245787620544434f, -5.455147743225098f, -2.2380611896514893f, 2.56819224357605f, -4.827456951141357f, 3.0360512733459473f, -0.6807429790496826f, -1.4343763589859009f, 7.185630798339844f, 2.376199245452881f, -0.9815430641174316f, -3.4156181812286377f, -0.3295425772666931f, 1.6498570442199707f, 1.9761145114898682f, -12.726014137268066f, 1.0567013025283813f, 1.5709710121154785f, 2.0479605197906494f, 2.604810953140259f, 1.692884922027588f, 2.070223808288574f, 3.042729139328003f, 0.9487152099609375f, 1.7540478706359863f, 0.254963755607605f, 2.351234197616577f, -2.4946744441986084f, -0.07564282417297363f, 4.913860321044922f, -0.752872109413147f, -2.137735605239868f, 0.21248027682304382f, -0.5711961984634399f, 1.3505796194076538f, -0.38196516036987305f, 3.0417635440826416f, -2.382321834564209f, 0.9481916427612305f, -1.3964842557907104f, -0.3904525637626648f, -26.529184341430664f, 2.350407600402832f, 1.3773480653762817f, -2.296009063720703f, 3.882507562637329f, 0.7941833734512329f, 3.7872369289398193f, -3.561253547668457f, 2.143061399459839f, 3.657202959060669f, -2.873176097869873f, 2.2322418689727783f, -3.475159168243408f, -2.544965982437134f, -1.662200689315796f, 1.9252630472183228f, 1.300224781036377f, -2.3552005290985107f, 5.149258613586426f, -3.227169990539551f, -0.2994517385959625f, -0.25692835450172424f, 1.23247230052948f, -1.049454927444458f, -1.5378562211990356f, -1.1751283407211304f, -1.9060910940170288f, 2.308335542678833f, -0.19028784334659576f, 0.17229536175727844f, -2.28204607963562f, -4.546176433563232f, 0.06133434176445007f, 0.28100094199180603f, -3.719806432723999f, 0.03374233841896057f, 0.9404799938201904f, 9.314294815063477f, -1.0169156789779663f, -3.530346155166626f, 1.0429959297180176f, 0.3776707351207733f, -1.7442224025726318f, -1.2719262838363647f, -2.386214256286621f, -0.20598125457763672f, -3.1011829376220703f, -3.523689031600952f, -0.8577737808227539f, -0.1523473858833313f, 2.648906707763672f, -0.5690749883651733f, -1.2920159101486206f, 2.804633617401123f, 0.08910030126571655f, 0.6617433428764343f, 2.7214181423187256f, -0.9289782047271729f, -0.17531965672969818f, 1.467264175415039f, -1.482218623161316f, -3.8186655044555664f, 1.3947794437408447f, -1.6309949159622192f, 1.7728172540664673f, -0.6714071035385132f, 1.2795732021331787f, -1.7862682342529297f, 0.7117303013801575f, -2.113863706588745f, 0.09789814054965973f, 0.19928136467933655f, 1.667722463607788f, -2.633737564086914f, 2.274996757507324f, 2.872189521789551f, 1.3585658073425293f, -0.056869179010391235f, -1.4577915668487549f, -2.4433202743530273f, 2.228029251098633f, 1.5947520732879639f, 2.0710887908935547f, -0.6562632322311401f, -1.1608322858810425f, -0.6225028038024902f, -2.224762439727783f, 2.5646398067474365f, -1.2051492929458618f, 0.7552199959754944f, 3.13028883934021f, -3.448556900024414f, -1.628006100654602f, 3.850862503051758f, -0.592828631401062f, -2.5657505989074707f, 0.561771810054779f, -0.7224605083465576f, -4.7110276222229f, -2.1388158798217773f, 1.5205360651016235f, -2.439723014831543f, 0.09085533022880554f, 0.7901652455329895f, -2.376593828201294f, -0.2709208130836487f, -2.826883316040039f, 0.7726789116859436f, 1.7271472215652466f, 0.5913077592849731f, 2.7742788791656494f, 2.0960631370544434f, 0.49165529012680054f, 2.2279205322265625f, 1.2907705307006836f, 2.1585774421691895f, -0.93156898021698f, 1.0188531875610352f, 1.612694263458252f, 3.080389976501465f, -0.16059909760951996f, 2.7825257778167725f, -0.6508034467697144f, -1.0849828720092773f, 0.7205013036727905f, 0.6836737394332886f, 3.169222593307495f, -0.07259383797645569f, -1.5128631591796875f, -5.746516704559326f, 2.8176681995391846f, -1.3532836437225342f, 2.489159345626831f, -1.532341480255127f, -2.2273266315460205f, -2.6067028045654297f, 3.2583539485931396f, 2.833320140838623f, 1.2422364950180054f, 3.880305290222168f, -3.6177821159362793f, -0.8864870071411133f, -0.3577319085597992f, 0.2378571629524231f, 0.24012884497642517f, -0.757179856300354f, 2.0959699153900146f, -1.2934008836746216f, 0.5416789054870605f, -2.83016300201416f, -0.02178722620010376f, -1.0416368246078491f, -2.4006197452545166f, -0.7035248279571533f, 2.133391857147217f, 0.7070752382278442f, 5.048967361450195f, 0.759548544883728f, -51.898292541503906f, 3.6495141983032227f, -4.508161544799805f, 0.8586408495903015f, -0.8683440685272217f, -0.5359578132629395f, 1.7059683799743652f, 2.6805572509765625f, 2.469728469848633f, -0.9354676008224487f, 0.30449068546295166f, -2.788017749786377f, 0.4161044955253601f, 0.4876648783683777f, 0.6499847769737244f, 0.7718979120254517f, -2.3910226821899414f, 1.085374116897583f, 0.04196920990943909f, 2.0038704872131348f, 2.2014026641845703f, 1.3917789459228516f, -0.8619474768638611f, -0.4745505452156067f, -2.5502536296844482f, -0.8737020492553711f, -32.76123809814453f, -1.7034552097320557f, -1.455233097076416f, 5.550527095794678f, 1.796770691871643f, 2.9735889434814453f, 0.8082617521286011f, -0.856388509273529f, 0.2139235883951187f, -0.6476525664329529f, 2.1954283714294434f, -0.6915762424468994f, -0.9847636222839355f, -2.962090015411377f, 5.514171600341797f, -1.6357896327972412f, -0.9447836875915527f, 3.029020071029663f, 2.1361303329467773f, 0.8624556064605713f, -1.4995063543319702f, 0.7835429310798645f, -0.9913151860237122f, -0.7837364077568054f, -8.906120300292969f, -0.5284900665283203f, -0.11144350469112396f, 0.24077343940734863f, -2.9645721912384033f, -1.4849615097045898f, 3.7856316566467285f, 0.5321260690689087f, 1.416678547859192f, -0.5095252394676208f, 0.17161962389945984f, 0.5733401775360107f, 3.873167037963867f, 2.5395700931549072f, -0.9699333310127258f, 2.229121208190918f, 2.8960535526275635f, 3.1946914196014404f, -2.9340779781341553f, -1.3670810461044312f, -1.9152945280075073f, 1.2845218181610107f, -1.1796507835388184f, 0.3448224663734436f, 1.6444532871246338f, -2.490063428878784f, -1.4516515731811523f, -0.2743357717990875f, 1.0306557416915894f, 3.651329517364502f, 0.6372451782226562f, -0.46017324924468994f, 0.018711626529693604f, 0.433815598487854f, 1.5432409048080444f, 2.5818984508514404f, -0.35323774814605713f, -1.3885042667388916f, 1.1344131231307983f, 0.47535037994384766f, -0.8028268814086914f, 4.211002349853516f, -1.3977311849594116f, -0.626477837562561f, -0.10317030549049377f, -0.45602285861968994f, 0.38029712438583374f, 2.379033088684082f, -7.1982102394104f, 0.23043206334114075f, -2.440929651260376f, 0.3542534112930298f, -1.601769208908081f, 1.4474791288375854f, -0.29290759563446045f, -9.19869327545166f, -1.4835008382797241f, -1.1244919300079346f, 0.182235985994339f, 6.482483386993408f, 1.0614184141159058f, 1.9869825839996338f, 0.5686616897583008f, -1.704241156578064f, 5.332779884338379f, -1.7723124027252197f, -1.5089197158813477f, -1.9783927202224731f, -1.535443902015686f, -5.259688377380371f, 1.3682790994644165f, 4.114401340484619f, -0.13576887547969818f, -2.438045024871826f, 1.9003021717071533f, -1.3053830862045288f, 1.7891337871551514f, -1.9632920026779175f, -1.8849395513534546f, -1.0678439140319824f, -2.610227584838867f, -4.588696479797363f, 2.0857489109039307f, 0.26437070965766907f, 0.5831907391548157f, -2.3644251823425293f, 1.1809320449829102f, -0.4051530659198761f, 1.6519569158554077f, -1.5457805395126343f, -0.4486098289489746f, 0.7831277847290039f, 1.9095414876937866f, -4.654076099395752f, -1.7016772031784058f, -0.07797497510910034f, 0.4647452235221863f, -1.0609700679779053f, -3.216202974319458f, 0.5690118074417114f, 1.9025346040725708f, 3.6386466026306152f, -0.8305952548980713f, -3.38120698928833f, 1.6708505153656006f, -2.5501277446746826f, 4.737482070922852f, 1.0650047063827515f, 2.0943634510040283f, 2.4048478603363037f, -1.269448161125183f, 1.001997709274292f, -3.1769533157348633f, -390.7607421875f, -1.4604345560073853f, 0.9623389840126038f, 0.6608952879905701f, 2.148923873901367f, -2.2716054916381836f, -1.9424495697021484f, -1.821995735168457f, 1.4867695569992065f, -3.2472758293151855f, 0.09403122216463089f, -4.469510555267334f, -0.30789482593536377f, 0.10543800890445709f, -2.9394564628601074f, 2.3430910110473633f, 2.5006608963012695f, -0.14535214006900787f, 2.7709193229675293f, 1.2657743692398071f, 1.9684560298919678f, 0.1936264932155609f, -0.9922794699668884f, 1.0531762838363647f, -1.4300163984298706f, 2.9359824657440186f, 1.919494867324829f, -0.547626793384552f, 1.2202463150024414f, 1.986266851425171f, 0.5679800510406494f, 0.574746310710907f, -2.2339823246002197f, 0.4776488244533539f, -0.4569471776485443f, 2.8697147369384766f, 2.404614210128784f, 0.06845444440841675f, 2.2238657474517822f, 0.810901939868927f, -1.9009513854980469f, 1.4094899892807007f, -2.4696571826934814f, 0.7078478932380676f, -0.7913968563079834f, -1.02341890335083f, -3.2423882484436035f, -2.362088203430176f, -1.1863514184951782f, 2.0137314796447754f, 0.12442287802696228f, -0.13140802085399628f, 1.081570029258728f, -1.2454830408096313f, -1.082194447517395f, 0.1312730610370636f, 0.49821850657463074f, -0.9824815988540649f, -0.7947803735733032f, 0.9182974696159363f, -2.276376485824585f, 1.520261526107788f, -0.06351828575134277f, -3.245898723602295f, -0.7689349055290222f, -0.09466087818145752f, -0.869961678981781f, -1.749258279800415f, -1.526936411857605f, -0.12629956007003784f, -0.7732882499694824f, -2.3623971939086914f, -3.1913340091705322f, -0.27194395661354065f, -0.39535653591156006f, 0.1678144931793213f, 2.2089595794677734f, -1.5523966550827026f, 2.043370485305786f, 3.143930196762085f, 1.2736519575119019f, 0.6925475597381592f, -0.4911692142486572f, -0.8852919340133667f, -1.0679302215576172f, 1.0655741691589355f, 0.29258009791374207f, 0.1546686440706253f, 0.44009071588516235f, 2.983549118041992f, 1.9041895866394043f, -3.745859384536743f, -3.180199146270752f, 2.681286334991455f, 2.6611733436584473f, 2.267343044281006f, -0.5568395256996155f, 0.6402643322944641f, -2.4629883766174316f, -0.871990442276001f, -0.22872933745384216f, -2.3832621574401855f, -1.517675757408142f, -1.281901478767395f, -1.5484514236450195f, 0.19533796608448029f, 1.5083162784576416f, 0.6675201654434204f, -1.8809876441955566f, -0.5741806626319885f, 1.6113348007202148f, 3.3245577812194824f, 0.8644391298294067f, 0.4192604422569275f, 1.377789855003357f, 0.6702362298965454f, 0.2238878607749939f, -1.1963272094726562f, -2.8303709030151367f, -2.2576804161071777f, -1.6235253810882568f, -3.462388277053833f, -0.1649545580148697f, -3.3871846199035645f, -2.5431580543518066f, 1.9949069023132324f, -2.0785109996795654f, 0.18136709928512573f, 1.443914532661438f, -0.15320733189582825f, 0.34971532225608826f, -0.8759166598320007f, 2.5320093631744385f, -2.7036967277526855f, -0.2109396755695343f, 2.179887533187866f, -0.5103341341018677f, -0.10834081470966339f, 1.6850608587265015f, 0.793129026889801f, -1.274688482284546f, 1.05690336227417f, -0.5267642736434937f, -3.5100746154785156f, 2.044546365737915f, 2.1082937717437744f, -1.7622865438461304f, -3.9578211307525635f, 2.519562244415283f, 0.8127772808074951f, -1.5711843967437744f, 2.255955934524536f, 1.8760515451431274f, -1.3153287172317505f, 0.9503816366195679f, -0.2653518319129944f, 0.4832201600074768f, -1.2045989036560059f, 2.653332233428955f, -1.840606927871704f, 5.3351287841796875f, 1.0200539827346802f, 3.4046459197998047f, -0.5007335543632507f, 0.8175743818283081f, 1.3271771669387817f, 2.951313018798828f, -4.919040679931641f, 0.9244036078453064f, -1.9821195602416992f, 0.6058339476585388f, 1.8174591064453125f, -1.8126754760742188f, -3.3048319816589355f, 1.3915607929229736f, -2.466024875640869f, -0.9790647625923157f, -1.9299893379211426f, 3.9352238178253174f, 2.4501376152038574f, 0.5523784160614014f, 1.6552584171295166f, 2.8175041675567627f, -1.5210044384002686f, 2.368471622467041f, -3.190028667449951f, 1.1798765659332275f, -3.9063186645507812f, -1.9406728744506836f, -5.581540107727051f, 1.450130581855774f, -1.8258417844772339f, 0.03147907555103302f, -1.8759326934814453f, 2.1764109134674072f, -0.6857712268829346f, 1.3103924989700317f, 0.7751880884170532f, -1.1145973205566406f, 3.2225217819213867f, 0.012596040964126587f, -2.4446616172790527f, 2.1669809818267822f, -0.7631680369377136f, 4.085460186004639f, 1.2307112216949463f, -4.6482133865356445f, 1.9159724712371826f, -0.1558103859424591f, 4.7661967277526855f, -2.8957154750823975f, 4.761860370635986f, 1.7035266160964966f, 1.2078752517700195f, 2.7105114459991455f, -1.761616587638855f, -0.09714099764823914f, 1.2714219093322754f, 2.5303597450256348f, 1.6374545097351074f, -0.7423419952392578f, -2.3692750930786133f, 1.541703701019287f, -0.8960074186325073f, 61.22904968261719f, -0.4218991696834564f, -2.2563068866729736f, 0.756054162979126f, -1.591499924659729f, 0.4610883593559265f, -0.6834573745727539f, -3.001169204711914f, 1.395485281944275f, 3.066460371017456f, 3.3393359184265137f, -3.585296392440796f, -2.3826780319213867f, 1.2217779159545898f, 2.3713793754577637f};
        List<Float> collect = Arrays.stream(floats).collect(Collectors.toList());
        return collect;
    }


    public static final Integer dimensions = 1024;

    @Test
    public void vectorWriteV1() throws Exception {
//        List<String> text = Stream.of("刘玄德", "桃园三结义", "刘备", "毛岸英", "毛泽东思想", "新中国", "江青", "杨开慧", "毛润之", "三国", "武当山", "二万五千里长征", "太极拳").collect(Collectors.toList());
        List<String> text = Stream.of(
//                "武当山不仅是中国道教文化的圣地，也是自然与人文景观的完美融合之地。游客在这里可以体验丰富的历史文化和壮丽的自然风光",
//                "庐山集自然美景与深厚文化底蕴于一体，无论是自然景观的雄奇险秀，还是人文景观的丰富多彩，基本都使其成为不可多得的旅游胜地。对于想要探访庐山的游客来说，春夏秋三季是最佳游览时节。"
//                "西安事变是**由张学良和杨虎城于1936年12月12日为劝谏蒋介石改变“攘外必先安内”的国策、停止内战一致抗日而发动的兵谏，又称双十二事变**。这次事变对我国近现代史产生了深远的影响，不仅因为其直接参与者的身份特殊，更因为它标志着国内战争向抗日战争转折的重大历史节点"
//                "大势智慧科技有限公司凭借其在实景三维数字化重建及三维数据服务的深厚技术积累和丰富的实践经验，已经成为该领域的领导者。面对未来的机遇与挑战，大势智慧有望继续发挥其在技术创新和行业应用方面的优势，为不同行业提供更多高质量的三维数字化解决方案，推动相关领域的技术进步和产业升级"
                "关羽", "张飞"
        ).collect(Collectors.toList());
        String url = "https://open.bigmodel.cn/api/paas/v4/embeddings";
        Map<String, String> header = new HashMap<>();
        header.put("Content-Type", "application/json");
        header.put("Authorization", "Bearer 4df471b3522e29be7c35892ff96823ed.ZglBOjAuli6BvhMb");

        for (String s : text) {
            List<Float> embeddings = new LinkedList<>();
            EmbeddingParams embeddingParams = CommonBuilder.of(EmbeddingParams.class)
                    .with(EmbeddingParams::setInput, ListBuilder.<String>ofList().add(s).build())
                    .build();
            String result = HttpRequestUtil.doPost(url, header, JSON.toJSONString(embeddingParams));
            JSONObject jsonObject = JSON.parseObject(result);
            JSONArray jsonArray = jsonObject.getJSONArray("data");
            JSONObject embeddingObj = (JSONObject) jsonArray.get(0);
            JSONArray embedding = embeddingObj.getJSONArray("embedding");
            for (Object o : embedding) {
                embeddings.add(((BigDecimal) o).floatValue());
            }
            MyVector myVector = CommonBuilder.of(MyVector.class)
                    .with(MyVector::setDepartment, s)
                    .with(MyVector::setEmbedding, embeddings)
                    .build();
            esClientUtil.writeData(myVector, IdUtil.getSnowflakeNextId() + "", "my_embedding");
        }
    }

    @Test
    public void vectorSearchV1() throws Exception {
//        List<String> text = Stream.of("刘备", "关羽", "毛泽东思想", "毛泽东", "林彪", "周恩来", "张三丰", "十堰").collect(Collectors.toList());
//        List<String> text = Stream.of("西安", "十堰", "江西", "张学良", "蒋介石", "杨虎城", "黄先锋").collect(Collectors.toList());
        List<String> text = Stream.of("桃园三结义", "十堰", "江西", "张学良", "蒋介石", "大势智慧", "黄先锋").collect(Collectors.toList());
        String url = "https://open.bigmodel.cn/api/paas/v4/embeddings";
        Map<String, String> header = new HashMap<>();
        header.put("Content-Type", "application/json");
        header.put("Authorization", "Bearer 4df471b3522e29be7c35892ff96823ed.ZglBOjAuli6BvhMb");

        for (String s : text) {
            List<Float> embeddings = new LinkedList<>();
            EmbeddingParams embeddingParams = CommonBuilder.of(EmbeddingParams.class)
                    .with(EmbeddingParams::setInput, ListBuilder.<String>ofList().add(s).build())
                    .build();
            String result = HttpRequestUtil.doPost(url, header, JSON.toJSONString(embeddingParams));
            JSONObject jsonObject = JSON.parseObject(result);
            JSONArray jsonArray = jsonObject.getJSONArray("data");
            JSONObject embeddingObj = (JSONObject) jsonArray.get(0);
            JSONArray embedding = embeddingObj.getJSONArray("embedding");
            for (Object o : embedding) {
                embeddings.add(((BigDecimal) o).floatValue());
            }
            // 设置查询向量
            KnnQuery.Builder builder = new KnnQuery.Builder();
            builder.field("embedding")
                    .numCandidates(100)
                    .k(10)
//                .similarity(0.3f)
                    .queryVector(embeddings);
            // 创建搜索请求
            SearchRequest searchRequest = new SearchRequest.Builder()
                    .index("my_embedding")
                    .knn(builder.build())
                    .size(10)
                    .build();

            // 执行搜索请求
            SearchResponse<Product> searchResponse = es8ClientUtil.searchData(searchRequest, "my_embedding", Product.class);
            StringJoiner stringJoiner = new StringJoiner("\t");
            StringJoiner score = new StringJoiner("\t");
            for (Hit<Product> hit : searchResponse.hits().hits()) {
                stringJoiner.add(hit.source().getDepartment());
                score.add(hit.score() + "");
            }
            System.err.println(s + "===检索结果===:");
            System.err.println(stringJoiner);
            System.err.println(score);
            System.err.println();
        }
    }

    @Test
    public void vectorWriteV2() throws Exception {
        List<String> text = Stream.of("刘玄德", "桃园三结义", "刘备", "毛岸英", "毛泽东思想", "新中国", "江青", "杨开慧", "毛润之", "三国", "武当山", "二万五千里长征", "太极拳").collect(Collectors.toList());
//        List<String> text = Stream.of(
//                "武当山不仅是中国道教文化的圣地，也是自然与人文景观的完美融合之地。游客在这里可以体验丰富的历史文化和壮丽的自然风光",
//                "庐山集自然美景与深厚文化底蕴于一体，无论是自然景观的雄奇险秀，还是人文景观的丰富多彩，基本都使其成为不可多得的旅游胜地。对于想要探访庐山的游客来说，春夏秋三季是最佳游览时节。"
//                "西安事变是**由张学良和杨虎城于1936年12月12日为劝谏蒋介石改变“攘外必先安内”的国策、停止内战一致抗日而发动的兵谏，又称双十二事变**。这次事变对我国近现代史产生了深远的影响，不仅因为其直接参与者的身份特殊，更因为它标志着国内战争向抗日战争转折的重大历史节点"
//                "大势智慧科技有限公司凭借其在实景三维数字化重建及三维数据服务的深厚技术积累和丰富的实践经验，已经成为该领域的领导者。面对未来的机遇与挑战，大势智慧有望继续发挥其在技术创新和行业应用方面的优势，为不同行业提供更多高质量的三维数字化解决方案，推动相关领域的技术进步和产业升级"
//        ).collect(Collectors.toList());
        String url = "https://dashscope.aliyuncs.com/api/v1/services/embeddings/text-embedding/text-embedding";
        Map<String, String> header = new HashMap<>();
        header.put("Content-Type", "application/json");
        header.put("Authorization", "Bearer sk-d237f087ed9d4e57935ee244c332c1f0");

        for (String s : text) {
            List<Float> ebembeddings = new LinkedList<>();
            EmbeddingParamsAliyun embeddingParams = CommonBuilder.of(EmbeddingParamsAliyun.class)
                    .with(EmbeddingParamsAliyun::setInput, CommonBuilder.of(Input.class).with(Input::setTexts, ListBuilder.<String>ofList().add(s).build()).build())
                    .with(EmbeddingParamsAliyun::setModel, "text-embedding-v3")
                    .build();
            String result = HttpRequestUtil.doPost(url, header, JSON.toJSONString(embeddingParams));
            JSONObject jsonObject = JSON.parseObject(result);
            JSONObject output = jsonObject.getJSONObject("output");
            JSONArray embeddings = output.getJSONArray("embeddings");
            JSONObject embeddingObj = (JSONObject) embeddings.get(0);
            JSONArray embedding = embeddingObj.getJSONArray("embedding");
            for (Object o : embedding) {
                ebembeddings.add(((BigDecimal) o).floatValue());
            }
            MyVector myVector = CommonBuilder.of(MyVector.class)
                    .with(MyVector::setDepartment, s)
                    .with(MyVector::setEmbedding, ebembeddings)
                    .build();
            esClientUtil.writeData(myVector, IdUtil.getSnowflakeNextId() + "", "my_embedding");
        }
    }

    @Test
    public void vectorSearchV2() throws Exception {
        List<String> text = Stream.of("刘备", "关羽", "毛泽东思想", "毛泽东", "林彪", "周恩来", "张三丰", "十堰").collect(Collectors.toList());
//        List<String> text = Stream.of("刘备", "关羽", "周恩来", "张三丰", "十堰").collect(Collectors.toList());
        String url = "https://dashscope.aliyuncs.com/api/v1/services/embeddings/text-embedding/text-embedding";
        Map<String, String> header = new HashMap<>();
        header.put("Content-Type", "application/json");
        header.put("Authorization", "Bearer sk-d237f087ed9d4e57935ee244c332c1f0");
        for (String s : text) {
            List<Float> ebembeddings = new LinkedList<>();
            EmbeddingParamsAliyun embeddingParams = CommonBuilder.of(EmbeddingParamsAliyun.class)
                    .with(EmbeddingParamsAliyun::setInput, CommonBuilder.of(Input.class).with(Input::setTexts, ListBuilder.<String>ofList().add(s).build()).build())
                    .with(EmbeddingParamsAliyun::setModel, "text-embedding-v3")
                    .build();
            String result = HttpRequestUtil.doPost(url, header, JSON.toJSONString(embeddingParams));
            JSONObject jsonObject = JSON.parseObject(result);
            JSONObject output = jsonObject.getJSONObject("output");
            JSONArray embeddings = output.getJSONArray("embeddings");
            JSONObject embeddingObj = (JSONObject) embeddings.get(0);
            JSONArray embedding = embeddingObj.getJSONArray("embedding");
            for (Object o : embedding) {
                ebembeddings.add(((BigDecimal) o).floatValue());
            }
            // 设置查询向量
            KnnQuery.Builder builder = new KnnQuery.Builder();
            builder.field("embedding")
                    .numCandidates(100)
                    .k(10)
//                .similarity(0.3f)
                    .queryVector(ebembeddings);
            // 创建搜索请求
            SearchRequest searchRequest = new SearchRequest.Builder()
                    .index("my_embedding")
                    .knn(builder.build())
                    .size(10)
                    .build();

            // 执行搜索请求
            SearchResponse<Product> searchResponse = es8ClientUtil.searchData(searchRequest, "my_embedding", Product.class);
            StringJoiner stringJoiner = new StringJoiner("\t");
            StringJoiner score = new StringJoiner("\t");
            for (Hit<Product> hit : searchResponse.hits().hits()) {
                stringJoiner.add(hit.source().getDepartment());
                score.add(hit.score() + "");
            }
            System.err.println(s + "===检索结果===:");
            System.err.println(stringJoiner);
            System.err.println(score);
            System.err.println();
        }
    }


    @Test
    public void vectorWriteV3() throws Exception {
//        List<String> text = Stream.of("刘玄德", "桃园三结义", "刘备", "毛岸英", "毛泽东思想", "新中国", "江青", "杨开慧", "毛润之", "三国", "武当山", "二万五千里长征", "太极拳").collect(Collectors.toList());
        List<String> text = Stream.of(
                "武当山不仅是中国道教文化的圣地，也是自然与人文景观的完美融合之地。游客在这里可以体验丰富的历史文化和壮丽的自然风光",
                "庐山集自然美景与深厚文化底蕴于一体，无论是自然景观的雄奇险秀，还是人文景观的丰富多彩，基本都使其成为不可多得的旅游胜地。对于想要探访庐山的游客来说，春夏秋三季是最佳游览时节。",
                "西安事变是**由张学良和杨虎城于1936年12月12日为劝谏蒋介石改变“攘外必先安内”的国策、停止内战一致抗日而发动的兵谏，又称双十二事变**。这次事变对我国近现代史产生了深远的影响，不仅因为其直接参与者的身份特殊，更因为它标志着国内战争向抗日战争转折的重大历史节点",
                "大势智慧科技有限公司凭借其在实景三维数字化重建及三维数据服务的深厚技术积累和丰富的实践经验，已经成为该领域的领导者。面对未来的机遇与挑战，大势智慧有望继续发挥其在技术创新和行业应用方面的优势，为不同行业提供更多高质量的三维数字化解决方案，推动相关领域的技术进步和产业升级"
        ).collect(Collectors.toList());
        String url = "https://api.baichuan-ai.com/v1/embeddings";
        Map<String, String> header = new HashMap<>();
        header.put("Content-Type", "application/json");
        header.put("Authorization", "Bearer sk-c82aeeb05232d9eed129dca242ebc61d");

        for (String s : text) {
            List<Float> embeddings = new LinkedList<>();
            EmbeddingParams embeddingParams = CommonBuilder.of(EmbeddingParams.class)
                    .with(EmbeddingParams::setInput, ListBuilder.<String>ofList().add(s).build())
                    .with(EmbeddingParams::setModel, "Baichuan-Text-Embedding")
                    .build();
            String result = HttpRequestUtil.doPost(url, header, JSON.toJSONString(embeddingParams));
            JSONObject jsonObject = JSON.parseObject(result);
            JSONArray jsonArray = jsonObject.getJSONArray("data");
            JSONObject embeddingObj = (JSONObject) jsonArray.get(0);
            JSONArray embedding = embeddingObj.getJSONArray("embedding");
            for (Object o : embedding) {
                embeddings.add(((BigDecimal) o).floatValue());
            }
            MyVector myVector = CommonBuilder.of(MyVector.class)
                    .with(MyVector::setDepartment, s)
                    .with(MyVector::setEmbedding, embeddings)
                    .build();
            esClientUtil.writeData(myVector, IdUtil.getSnowflakeNextId() + "", "baichuan_embedding");
        }
    }

    @Test
    public void vectorSearchV3() throws Exception {
//        List<String> text = Stream.of("刘备", "关羽", "毛泽东思想", "毛泽东", "林彪", "周恩来", "张三丰", "十堰").collect(Collectors.toList());
//        List<String> text = Stream.of("西安", "十堰", "江西", "张学良", "蒋介石", "杨虎城", "黄先锋").collect(Collectors.toList());
        List<String> text = Stream.of("桃园三结义").collect(Collectors.toList());
        String url = "https://api.baichuan-ai.com/v1/embeddings";
        Map<String, String> header = new HashMap<>();
        header.put("Content-Type", "application/json");
        header.put("Authorization", "Bearer sk-c82aeeb05232d9eed129dca242ebc61d");
        for (String s : text) {
            List<Float> embeddings = new LinkedList<>();
            EmbeddingParams embeddingParams = CommonBuilder.of(EmbeddingParams.class)
                    .with(EmbeddingParams::setInput, ListBuilder.<String>ofList().add(s).build())
                    .with(EmbeddingParams::setModel, "Baichuan-Text-Embedding")
                    .build();
            String result = HttpRequestUtil.doPost(url, header, JSON.toJSONString(embeddingParams));
            JSONObject jsonObject = JSON.parseObject(result);
            JSONArray jsonArray = jsonObject.getJSONArray("data");
            JSONObject embeddingObj = (JSONObject) jsonArray.get(0);
            JSONArray embedding = embeddingObj.getJSONArray("embedding");
            for (Object o : embedding) {
                embeddings.add(((BigDecimal) o).floatValue());
            }
            // 设置查询向量
            KnnQuery.Builder builder = new KnnQuery.Builder();
            builder.field("embedding")
                    .numCandidates(100)
                    .k(10)
//                .similarity(0.3f)
                    .queryVector(embeddings);
            // 创建搜索请求
            SearchRequest searchRequest = new SearchRequest.Builder()
                    .index("baichuan_embedding")
                    .knn(builder.build())
                    .size(10)
                    .build();

            // 执行搜索请求
            SearchResponse<Product> searchResponse = es8ClientUtil.searchData(searchRequest, "baichuan_embedding", Product.class);
            StringJoiner stringJoiner = new StringJoiner("\t");
            StringJoiner score = new StringJoiner("\t");
            for (Hit<Product> hit : searchResponse.hits().hits()) {
                stringJoiner.add(hit.source().getDepartment());
                score.add(hit.score() + "");
            }
            System.err.println(s + "===检索结果===:");
            System.err.println(stringJoiner);
            System.err.println(score);
            System.err.println();
        }
    }
}
