package com.das.resourceaction;

import com.das.BaseTest;
import com.pig4cloud.pigx.data.center.mapper.ResourceActionMapper;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.Map;


public class ActionTest extends BaseTest {

    @Autowired
    private ResourceActionMapper resourceActionMapper;


    @Test
    public void testConsume() {
        List<Map<String, Object>> list = List.of(Map.of("tableName", "t_meta_data_16001001_3", "resourceIds", List.of(1833768938688032768L)),
                Map.of("tableName", "t_meta_data_06003001_1", "resourceIds", List.of(1831952147938590720L)));
        System.out.println(list);
        resourceActionMapper.resumeResourceAction(list);
    }
}
