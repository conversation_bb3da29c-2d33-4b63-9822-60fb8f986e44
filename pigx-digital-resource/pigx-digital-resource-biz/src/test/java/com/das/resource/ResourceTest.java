package com.das.resource;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.das.BaseTest;
import com.pig4cloud.pigx.common.core.util.CommonBuilder;
import com.pig4cloud.pigx.data.center.entity.query.ModelDataQuery;
import com.pig4cloud.pigx.data.center.es.response.CountResult;
import com.pig4cloud.pigx.data.center.es.service.ResourceEsService;
import com.pig4cloud.pigx.data.center.service.DataAssetsService;
import com.pig4cloud.pigx.data.center.service.ResourceService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.Map;

public class ResourceTest extends BaseTest {

    @Autowired
    private ResourceService resourceService;

    @Autowired
    private DataAssetsService dataAssetsService;

    @Autowired
    private ResourceEsService resourceEsService;

    @Test
    public void cardCount() {
        ModelDataQuery modelDataQuery = CommonBuilder.of(ModelDataQuery.class)
                .build();
        List<CountResult> countResults = resourceService.cardCount(modelDataQuery);
        System.out.println(JSON.toJSONString(countResults, SerializerFeature.PrettyFormat));
    }


    @Test
    public void countMetaData() {
        ModelDataQuery modelDataQuery = CommonBuilder.of(ModelDataQuery.class)
                .with(ModelDataQuery::setCatalogNo, "02001002")
                .build();
        List<CountResult> countResults = dataAssetsService.countMetaData(modelDataQuery);
        System.out.println(JSON.toJSONString(countResults, SerializerFeature.PrettyFormat));
    }

    @Test
    public void countByCatalog() {
        ModelDataQuery modelDataQuery = CommonBuilder.of(ModelDataQuery.class)
                .with(ModelDataQuery::setCatalogNo, "02001002")
                .build();
        List<CountResult> countResults = resourceEsService.countByCatalog(modelDataQuery);
        System.out.println(JSON.toJSONString(countResults, SerializerFeature.PrettyFormat));
    }

    @Test
    public void trendChart() {
        ModelDataQuery modelDataQuery = CommonBuilder.of(ModelDataQuery.class)
                .with(ModelDataQuery::setCatalogNo, "02001002")
                .build();
        Map<String, List<CountResult>> stringListMap = resourceService.trendChart(modelDataQuery);
        System.out.println(JSON.toJSONString(stringListMap, SerializerFeature.PrettyFormat));
    }

    @Test
    public void deptRange() {
        ModelDataQuery modelDataQuery = CommonBuilder.of(ModelDataQuery.class)
                .with(ModelDataQuery::setTimeType, 1)
                .build();
        List<CountResult> countResults = resourceService.deptRange(modelDataQuery);
        System.out.println(JSON.toJSONString(countResults, SerializerFeature.PrettyFormat));
    }
}
