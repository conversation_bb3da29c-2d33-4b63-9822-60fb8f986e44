package com.das.apptest;

import cn.hutool.core.util.IdUtil;
import com.alibaba.fastjson.JSON;
import com.pig4cloud.pigx.common.core.util.CommonBuilder;
import com.pig4cloud.pigx.common.core.util.ListBuilder;
import com.pig4cloud.pigx.data.center.entity.ColumnInfo;
import org.junit.jupiter.api.Test;

import java.util.List;

public class AppTest {
    //资源名称
    //资源编码
    //创建人
    //创建时间 资源所属表名 资源id(business_id) 资源类型
    @Test
    public void format() {
        String CREATE_TABLE = "CREATE TABLE if NOT EXISTS public.%s(id SERIAL PRIMARY KEY);";
        System.out.println(String.format(CREATE_TABLE, "t_meta_data_02001005_2"));
        List<ColumnInfo> defaultColumns = ListBuilder.<ColumnInfo>ofList().add(CommonBuilder.of(ColumnInfo.class)
                        .with(ColumnInfo::setRemark, "主键id")
                        .with(ColumnInfo::setName, "id")
                        .with(ColumnInfo::setColumnRule, "{\"uniqueValue\":true,\"mustFilee\":true,\"readOnly\":true,\"desensitization\":true}")
                        .with(ColumnInfo::setDataType, "BIGSERIAL PRIMARY KEY")
                        .with(ColumnInfo::setUseState, 1)
                        .with(ColumnInfo::setTypeCode, "number")
                        .build())
                .add(CommonBuilder.of(ColumnInfo.class)
                        .with(ColumnInfo::setRemark, "资产编号")
                        .with(ColumnInfo::setName, "assets_num")
                        .with(ColumnInfo::setDataType, "varchar(32)")
                        .with(ColumnInfo::setUseState, 1)
                        .with(ColumnInfo::setTypeCode, "oneLine")
                        .with(ColumnInfo::setColumnRule, "{\"uniqueValue\":true,\"mustFilee\":true,\"readOnly\":true,\"desensitization\":true}")
                        .build())
                .add(CommonBuilder.of(ColumnInfo.class)
                        .with(ColumnInfo::setRemark, "创建人")
                        .with(ColumnInfo::setName, "create_user")
                        .with(ColumnInfo::setDataType, "varchar(32)")
                        .with(ColumnInfo::setUseState, 1)
                        .with(ColumnInfo::setTypeCode, "oneLine")
                        .with(ColumnInfo::setColumnRule, "{\"uniqueValue\":true,\"mustFilee\":true,\"readOnly\":true,\"desensitization\":true}")
                        .build())
                .add(CommonBuilder.of(ColumnInfo.class)
                        .with(ColumnInfo::setRemark, "创建时间")
                        .with(ColumnInfo::setName, "create_time")
                        .with(ColumnInfo::setDataType, "date")
                        .with(ColumnInfo::setUseState, 1)
                        .with(ColumnInfo::setTypeCode, "date")
                        .with(ColumnInfo::setColumnRule, "{\"uniqueValue\":true,\"mustFilee\":true,\"readOnly\":true,\"desensitization\":true}")
                        .build())
                .add(CommonBuilder.of(ColumnInfo.class)
                        .with(ColumnInfo::setRemark, "资产名称")
                        .with(ColumnInfo::setName, "assets_name")
                        .with(ColumnInfo::setDataType, "varchar(32)")
                        .with(ColumnInfo::setUseState, 1)
                        .with(ColumnInfo::setTypeCode, "oneLine")
                        .with(ColumnInfo::setColumnRule, "{\"uniqueValue\":true,\"mustFilee\":true,\"readOnly\":true,\"desensitization\":true}")
                        .build())
                .add(CommonBuilder.of(ColumnInfo.class)
                        .with(ColumnInfo::setRemark, "资产类型")
                        .with(ColumnInfo::setName, "assets_type")
                        .with(ColumnInfo::setDataType, "INT2")
                        .with(ColumnInfo::setUseState, 1)
                        .with(ColumnInfo::setTypeCode, "number")
                        .with(ColumnInfo::setColumnRule, "{\"uniqueValue\":true,\"mustFilee\":true,\"readOnly\":true,\"desensitization\":true}")
                        .build())
                .build();
        System.err.println(JSON.toJSONString(defaultColumns));
    }

    @Test
    public void snowFlake() {
        System.err.println(IdUtil.getSnowflakeNextId());
    }
}
