<?xml version="1.0" encoding="UTF-8"?>
<!--
  Copyright 1999-2018 Alibaba Group Holding Ltd.
  Licensed under the Apache License, Version 2.0 (the "License");
  you may not use this file except in compliance with the License.
  You may obtain a copy of the License at
       http://www.apache.org/licenses/LICENSE-2.0
  Unless required by applicable law or agreed to in writing, software
  distributed under the License is distributed on an "AS IS" BASIS,
  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  See the License for the specific language governing permissions and
  limitations under the License.
 -->
<project xmlns="http://maven.apache.org/POM/4.0.0"
		 xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
		 xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>com.pig4cloud</groupId>
		<artifactId>pigx</artifactId>
		<version>5.3.0</version>
	</parent>

	<artifactId>pigx-register</artifactId>
	<packaging>jar</packaging>
	<name>pigx-register</name>
	<description>nacos 注册配置中心</description>

	<properties>
		<nacos.version>2.2.4-OEM</nacos.version>
		<pig4cloud-plugin-dm8.version>0.0.2</pig4cloud-plugin-dm8.version>
	</properties>

	<dependencies>
		<dependency>
			<groupId>io.springboot.nacos</groupId>
			<artifactId>nacos-config</artifactId>
			<version>${nacos.version}</version>
		</dependency>
<!--		<dependency>-->
<!--			<groupId>org.apache.tomcat.embed</groupId>-->
<!--			<artifactId>tomcat-embed-jasper</artifactId>-->
<!--		</dependency>-->

		<dependency>
			<groupId>io.springboot.nacos</groupId>
			<artifactId>nacos-naming</artifactId>
			<version>${nacos.version}</version>
		</dependency>

		<dependency>
			<groupId>io.springboot.nacos</groupId>
			<artifactId>nacos-istio</artifactId>
			<version>${nacos.version}</version>
		</dependency>

		<dependency>
			<groupId>io.springboot.nacos</groupId>
			<artifactId>nacos-plugin-default-impl</artifactId>
			<version>${nacos.version}</version>
		</dependency>

		<dependency>
			<groupId>io.springboot.nacos</groupId>
			<artifactId>nacos-prometheus</artifactId>
			<version>${nacos.version}</version>
		</dependency>

		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-security</artifactId>
		</dependency>

		<dependency>
			<groupId>cn.hutool</groupId>
			<artifactId>hutool-system</artifactId>
		</dependency>

		<!--达梦数据库插件-->
		<dependency>
			<groupId>com.pig4cloud.plugin</groupId>
			<artifactId>nacos-datasource-plugin-dm8</artifactId>
			<version>${pig4cloud-plugin-dm8.version}</version>
		</dependency>

		<dependency>
			<groupId>com.dameng</groupId>
			<artifactId>DmJdbcDriver18</artifactId>
		</dependency>
	</dependencies>

	<build>
		<plugins>
			<plugin>
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-maven-plugin</artifactId>
			</plugin>
			<plugin>
				<groupId>io.fabric8</groupId>
				<artifactId>docker-maven-plugin</artifactId>
			</plugin>
		</plugins>
		<resources>
			<resource>
				<directory>src/main/resources</directory>
				<filtering>true</filtering>
				<excludes>
					<exclude>**/*.woff</exclude>
					<exclude>**/*.woff2</exclude>
					<exclude>**/*.ttf</exclude>
					<exclude>**/*.eot</exclude>
				</excludes>
			</resource>
			<resource>
				<directory>src/main/resources</directory>
				<filtering>false</filtering>
				<includes>
					<include>**/*.woff</include>
					<include>**/*.woff2</include>
					<include>**/*.ttf</include>
					<include>**/*.eot</include>
				</includes>
			</resource>
		</resources>
	</build>
</project>
