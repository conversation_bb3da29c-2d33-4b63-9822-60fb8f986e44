<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pig4cloud.pigx.yun.mapper.ArSourceInfoMapper">

    <resultMap id="BaseResultMap" type="com.pig4cloud.pigx.yun.entity.ArSourceInfo">
            <id property="id" column="id" />
            <result property="culturalName" column="cultural_name" />
            <result property="age" column="age" />
            <result property="texture" column="texture" />
            <result property="type" column="type" />
            <result property="gradeEvaluationId" column="grade_evaluation_id" />
            <result property="saveState" column="save_state" />
            <result property="sizeNote" column="size_note" />
            <result property="culturalIntroduce" column="cultural_introduce" />
            <result property="pictureName" column="picture_name" />
            <result property="pictureUrl" column="picture_url" />
            <result property="photoUrl" column="photo_url" />
            <result property="modelStatus" column="model_status" />
            <result property="modelOldUrl" column="model_old_url" />
            <result property="modelUrl" column="model_url" />
            <result property="audioUrl" column="audio_url" />
            <result property="videoUrl" column="video_url" />
            <result property="positionPoint" column="position_point" />
            <result property="remark" column="remark" />
            <result property="gmtCreate" column="gmt_create" />
            <result property="gmtModified" column="gmt_modified" />
    </resultMap>

    <sql id="Base_Column_List">
        id,cultural_name,age,texture,type,grade_evaluation_id,
        save_state,size_note,cultural_introduce,picture_name,picture_url,
        photo_url,model_status,model_old_url,model_url,audio_url,
        video_url,position_point,remark,gmt_create,gmt_modified
    </sql>
</mapper>
