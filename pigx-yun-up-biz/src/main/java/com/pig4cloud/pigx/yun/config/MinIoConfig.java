package com.pig4cloud.pigx.yun.config;


import com.pig4cloud.pigx.yun.util.MinioUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
@Slf4j
public class MinIoConfig {

    @Value("${minio.visitUrl}")
    private String visitUrl;
    @Value("${minio.endpoint}")
    private String endpoint;
    @Value("${minio.bucketName}")
    private String bucketName;
    @Value("${minio.accessKey}")
    private String accessKey;
    @Value("${minio.secretKey}")
    private String secretKey;

    @Bean
    public MinioUtils creatMinIoClient() {
        return new MinioUtils(visitUrl,endpoint, bucketName, accessKey, secretKey);
    }

}
