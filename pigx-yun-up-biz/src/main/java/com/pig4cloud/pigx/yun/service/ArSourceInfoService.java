package com.pig4cloud.pigx.yun.service;
import com.baomidou.mybatisplus.extension.service.IService;
import com.pig4cloud.pigx.yun.entity.ArSourceInfo;

/**
* <AUTHOR>
* @description 针对表【ar_source_info(文物资源基础信息表)】的数据库操作Service
* @createDate 2025-05-07 11:21:57
*/
public interface ArSourceInfoService extends IService<ArSourceInfo> {

    void addSourceInfo(ArSourceInfo arSourceInfo) throws Exception;

    void updateSourceInfo(ArSourceInfo arSourceInfo) throws Exception;

    void deleteSourceInfo(Long id);



}
