package com.pig4cloud.pigx.yun.controller;

import com.pig4cloud.pigx.yun.service.CollectionSourceInfoService;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpHeaders;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 云上九华-文物资源管理
 */
@Slf4j
@RestController
@RequestMapping("/collection")
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class CollectionSourceController {

    @Resource
    private CollectionSourceInfoService collectionSourceInfoService;

}
