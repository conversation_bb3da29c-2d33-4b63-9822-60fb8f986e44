package com.pig4cloud.pigx.yun.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.pig4cloud.pigx.yun.entity.CollectionSourceInfo;
import com.pig4cloud.pigx.yun.service.CollectionSourceInfoService;
import com.pig4cloud.pigx.yun.mapper.CollectionSourceInfoMapper;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【collection_source_info(文物资源基础信息表)】的数据库操作Service实现
* @createDate 2025-08-15 14:47:41
*/
@Service
public class CollectionSourceInfoServiceImpl extends ServiceImpl<CollectionSourceInfoMapper, CollectionSourceInfo>
    implements CollectionSourceInfoService{

}




