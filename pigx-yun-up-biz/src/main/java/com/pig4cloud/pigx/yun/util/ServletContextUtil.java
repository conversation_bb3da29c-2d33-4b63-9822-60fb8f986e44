package com.pig4cloud.pigx.yun.util;

import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;
import org.springframework.web.context.WebApplicationContext;

import javax.servlet.ServletContext;

/**
 * Description TODO
 *
 * <AUTHOR>
 * @since 2020/5/27
 */
@Component
public class ServletContextUtil implements ApplicationContextAware {
    private static ServletContext servletContext = null;
    private static WebApplicationContext webApplicationContext;

    private ServletContextUtil() {
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        webApplicationContext = (WebApplicationContext) applicationContext;
    }

    public synchronized static ServletContext get() {
        if (null == servletContext) {
            servletContext = webApplicationContext.getServletContext();
        }
        return servletContext;
    }
}
