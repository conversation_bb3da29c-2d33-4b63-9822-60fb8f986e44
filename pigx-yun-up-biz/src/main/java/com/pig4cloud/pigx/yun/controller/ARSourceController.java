package com.pig4cloud.pigx.yun.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.pig4cloud.pigx.admin.api.entity.SysTreeDicEntity;
import com.pig4cloud.pigx.admin.api.feign.RemoteDictTreeService;
import com.pig4cloud.pigx.yun.dto.ArSourceInfoListDTO;
import com.pig4cloud.pigx.yun.entity.ArSourceInfo;
import com.pig4cloud.pigx.yun.service.ArSourceInfoService;
import com.pig4cloud.pigx.yun.util.MinioUtils;
import com.pig4cloud.pigx.common.core.util.R;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;


/**
 * 云上九华-文物资源管理
 */
@Slf4j
@RestController
@RequestMapping("/ar")
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class ARSourceController {

    @Resource
    private ArSourceInfoService arSourceInfoService;

    @Resource
    private RemoteDictTreeService remoteDictTreeService;

    /**
     * 新增AR图书资源
     * @return
     */
    @Operation(summary = "新增AR图书资源", description = "新增AR图书资源")
    @PostMapping(value = "/add")
    public R addSourceInfo(@RequestBody ArSourceInfo arSourceInfo) {
        try {
            arSourceInfoService.addSourceInfo(arSourceInfo);
        }catch (Exception e){
            return R.failed(e.getMessage());
        }
        return R.ok();
    }

    @Operation(summary = "修改AR图书资源", description = "修改AR图书资源")
    @PutMapping(value = "/update")
    public R updateSourceInfo(@RequestBody ArSourceInfo arSourceInfo) {
        try {
            arSourceInfoService.updateSourceInfo(arSourceInfo);
        }catch (Exception e){
            return R.failed(e.getMessage());
        }
        return R.ok();
    }

    @Operation(summary = "删除AR图书资源", description = "删除AR图书资源")
    @DeleteMapping("/delete/{id}")
    public R delete(@PathVariable Long id) {
        try{
            arSourceInfoService.deleteSourceInfo(id);
        }catch (Exception e){
            return R.failed(e.getMessage());
        }
        return R.ok();
    }

    /**
     * 分页查询
     *
     * @param page 分页对象
     * @return
     */
    @Operation(summary = "分页查询", description = "分页查询")
    @GetMapping("/page")
    public R getArSourceInfoList(@ParameterObject Page page, @ParameterObject ArSourceInfoListDTO dto) {
        LambdaQueryWrapper<ArSourceInfo> queryWrapper = new LambdaQueryWrapper<>();
        if(StringUtils.isNotBlank(dto.getCulturalName())){
            queryWrapper.like(ArSourceInfo::getCulturalName, dto.getCulturalName());
        }
        if(StringUtils.isNotBlank(dto.getType())){
            queryWrapper.eq(ArSourceInfo::getType, dto.getType());
        }
        queryWrapper.orderByDesc(ArSourceInfo::getGmtCreate);
        IPage<ArSourceInfo> coCollectionInfoPage = arSourceInfoService.page(page, queryWrapper);
        buildPageRecords(coCollectionInfoPage);
        return R.ok(coCollectionInfoPage);
    }

    /**
     * 查询详情
     * @param id
     * @return
     */
    @GetMapping("/get/{id}")
    public R getArSourceInfo(@PathVariable Long id) {
        ArSourceInfo arSourceInfo = arSourceInfoService.getById(id);
        return R.ok(arSourceInfo);
    }

    /**
     * 获取文件临时访问地址
     * @param fileUrl
     * @return
     */
    @GetMapping("/getFileUrl")
    public R<List<String>> getArSourceInfo(@RequestParam("fileUrl") String fileUrl) {
        List<String> urlList = new ArrayList<>();
        if(StringUtils.isNotBlank(fileUrl)){
            List<String> photoUrlList = Arrays.asList(fileUrl.split(","));
            urlList = photoUrlList.stream().map(MinioUtils::getVisitUrl).collect(Collectors.toList());
        }
        return R.ok(urlList);
    }

    private void buildPageRecords(IPage<ArSourceInfo> arSourceInfoIPage) {
        if (!arSourceInfoIPage.getRecords().isEmpty()) {
            List<ArSourceInfo> coCollectionDetailPage = arSourceInfoIPage.getRecords();
            // 查询当前分页中的藏品关联的附件
            arSourceInfoIPage.setRecords(coCollectionDetailPage.stream().peek(item -> {
                if(StringUtils.isNotBlank(item.getAge())){
                    item.setAge(getTreeDic(item.getAge()));
                }
                if(StringUtils.isNotBlank(item.getTexture())) {
                    item.setTexture(getTreeDic(item.getTexture()));
                }
                if(StringUtils.isNotBlank(item.getPictureUrl())){
                    item.setPictureUrl(MinioUtils.getVisitUrl(item.getPictureUrl()));
                }
//                if(StringUtils.isNotBlank(item.getPhotoUrl())){
//                    List<String> photoUrlList = Arrays.asList(item.getPhotoUrl().split(","));
//                    List<String> urlList = photoUrlList.stream().map(url -> MinioUtils.getTempVisitUrl(MinioUtils.getBucketName(), url)).collect(Collectors.toList());
//                    item.setPhotoUrl(String.join(",", urlList));
//                }
//                if(StringUtils.isNotBlank(item.getPictureUrl())) {
//                    item.setModelUrl(MinioUtils.getTempVisitUrl(MinioUtils.getBucketName(), item.getModelUrl()));
//                }
//                if(StringUtils.isNotBlank(item.getAudioUrl())) {
//                    item.setAudioUrl(MinioUtils.getTempVisitUrl(MinioUtils.getBucketName(), item.getAudioUrl()));
//                }
//                if(StringUtils.isNotBlank(item.getVideoUrl())) {
//                    item.setVideoUrl(MinioUtils.getTempVisitUrl(MinioUtils.getBucketName(), item.getVideoUrl()));
//                }
            }).collect(Collectors.toList()));
        }
    }

    /**
     * 获取年代
     * @param age
     * @return
     */
    public String getTreeDic(String age) {
        String treeDicName = "";
        if (StringUtils.isNotEmpty(age)) {
            StringBuilder ageName = new StringBuilder();
            List<String> ageList = Arrays.asList(age.split(","));
            for (String key : ageList) {
                R<SysTreeDicEntity> data = remoteDictTreeService.getSysTreeDicEntityById(Long.valueOf(key));
                if (data.getData() != null) {
                    ageName.append(data.getData().getName()).append("/");
                }
            }
            treeDicName = ageName.substring(0, ageName.length() - 1);
        }
        return treeDicName;
    }

}
