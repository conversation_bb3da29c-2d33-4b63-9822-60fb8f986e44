package com.pig4cloud.pigx.yun.util;

import lombok.extern.slf4j.Slf4j;

import java.net.URLEncoder;

/**
 * Description 百度图像识别工具类
 *
 * <AUTHOR>
 * @since 2020/5/26
 */
@Slf4j
public class AdvancedGeneral {

    /**
     * 相似图片检索
     */
    private static final String addSameUrl = "https://aip.baidubce.com/rest/2.0/image-classify/v1/realtime_search/similar/add";
    private static final String searchSameUrl = "https://aip.baidubce.com/rest/2.0/image-classify/v1/realtime_search/similar/search";
    private static final String deleteSameUrl = "https://aip.baidubce.com/rest/2.0/image-classify/v1/realtime_search/similar/delete";

    /**
     * 上传图片
     *
     * @param base64 图片base64
     * @param name   图片名称
     * @param guid   关联guid
     * @return String
     */
    public static String sameHqAdd(String base64, String name, String guid) throws Exception {
        String imgParam = URLEncoder.encode(base64, "UTF-8");
        String param = "brief=" + "{\"name\":\"" + name + "\", \"guid\":\"" + guid + "\"}" + "&image=" + imgParam + "&tags=" + "1,1";
        return HttpUtil.post(addSameUrl, AccessTokenUtil.getAccessToken(), param);
    }

    /**
     * 获取相同图片列表
     */
    public static String getSameSearch(String base64) {
        try {
            String imgParam = URLEncoder.encode(base64, "UTF-8");
            String accessToken = AccessTokenUtil.getAccessToken();
            String param = "image=" + imgParam;
            return HttpUtil.post(searchSameUrl, accessToken, param);
        } catch (Exception e) {
            log.error("获取相同图片失败", e);
        }
        return null;
    }

    /**
     * 批量删除
     *
     * @return String
     */
    public static void deleteSameSearch(String cont_sign) {
        try {
            String accessToken = AccessTokenUtil.getAccessToken();
            String param = "cont_sign=" + cont_sign;
            HttpUtil.post(deleteSameUrl, accessToken, param);
        } catch (Exception e) {
            log.error("删除相同图片失败", e);
        }
    }
}
