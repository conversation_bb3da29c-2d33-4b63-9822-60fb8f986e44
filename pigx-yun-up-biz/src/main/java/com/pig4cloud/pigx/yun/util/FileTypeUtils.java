package com.pig4cloud.pigx.yun.util;

import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.List;

public class FileTypeUtils {

    /**
     * 图片类型枚举
     */
    public final static String[] PICTURE_TYPE = {"jpg", "png", "jpeg", "bmp", "gif", "webp", "svg", "psd", "tiff", "tif"};
    /**
     * 文档类型枚举
     */
    public final static String[] DOC_TYPE = {"doc", "docx", "xls", "xlsx", "pdf", "ppt", "pptx"};
    /**
     * 音频类型枚举
     */
    public final static String[] AUDIO_TYPE = {"mp3"};
    /**
     * 视频类型枚举
     */
    public final static String[] VIDEO_TYPE = {"mp4"};
    /**
     * 压缩包类型枚举
     */
    public final static String[] ZIP_TYPE = {"zip", "rar"};

    /**
     * OCR 可识别文件类型枚举
     */
    public final static String[] OCR_TYPE = {"jpg", "png", "jpeg", "bmp", "gif", "pdf"};


    public static List<String> getAllType() {
        List<String> list = new java.util.ArrayList<>();
        list.addAll(Arrays.asList(PICTURE_TYPE));
        list.addAll(Arrays.asList(DOC_TYPE));
        list.addAll(Arrays.asList(AUDIO_TYPE));
        list.addAll(Arrays.asList(VIDEO_TYPE));
        list.addAll(Arrays.asList(ZIP_TYPE));
        return list;
    }

    /**
     * 获取文件类型
     *
     * @param resSuffix
     * @return 1 图片 2 文档 3 音频 4 视频 5 压缩包
     */
    public static Byte getFileType(String resSuffix) {
        if (StringUtils.isBlank(resSuffix)) {
            return 0;
        }

        for (String str : PICTURE_TYPE) {
            if (resSuffix.equals(str)) {
                return 1;
            }
        }
        for (String str : DOC_TYPE) {
            if (resSuffix.equals(str)) {
                return 2;
            }
        }
        for (String str : AUDIO_TYPE) {
            if (resSuffix.equals(str)) {
                return 3;
            }
        }
        for (String str : VIDEO_TYPE) {
            if (resSuffix.equals(str)) {
                return 4;
            }
        }
        for (String str : ZIP_TYPE) {
            if (resSuffix.equals(str)) {
                return 5;
            }
        }

        return 0;
    }

}
