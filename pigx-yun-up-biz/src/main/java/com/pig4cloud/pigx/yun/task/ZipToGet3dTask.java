package com.pig4cloud.pigx.yun.task;


import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.pig4cloud.pigx.yun.entity.ArSourceInfo;
import com.pig4cloud.pigx.yun.mapper.ArSourceInfoMapper;
import com.pig4cloud.pigx.yun.util.CommandUtil;
import com.pig4cloud.pigx.yun.util.DateUtils;
import com.pig4cloud.pigx.yun.util.MinioUtils;
import com.pig4cloud.pigx.yun.util.UUIDUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.Charsets;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.*;
import java.nio.charset.Charset;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.Enumeration;
import java.util.LinkedList;
import java.util.List;
import java.util.zip.ZipEntry;
import java.util.zip.ZipFile;

/**
 * 模型转换定时任务
 *
 * @Author: shaochengwei
 * @Date: 2022-11-15
 */
@Component
@Slf4j
@EnableScheduling
public class ZipToGet3dTask {

    private LinkedList<File> filesPath;

    @Value("${ftp.uploadConf.tempFtpPath}")
    private String tempFtpPath;

    @Value("${minio.bucketName}")
    private String bucketName;

    @Resource
    private ArSourceInfoMapper arSourceInfoMapper;

    private final static String MODEL_JSON_CONTENT = "{\"metadata\": {\"version\": 1},\"gltfs\": [{\"url\": \"./{}\",\"name\": \"{}\"}]," +
            "\"images\": [],\"textures\": [],\"skeletons\": [],\"materials\": [],\"animations\": [],\"object\": {}}";



    /**
     * 模型转换定时任务
     */
    @Scheduled(cron = "0 0/2 * * * ?")
    public void unzipModel() {
        try {
            unzipModelService();
        }catch (Exception e){
            log.error("模型转换失败！",e);
        }
    }

    public void unzipModelService() {
        QueryWrapper<ArSourceInfo> wrapper = new QueryWrapper<>();
        wrapper.lambda().in(ArSourceInfo::getModelStatus, 0,2);
        List<ArSourceInfo> arSourceInfoList = this.arSourceInfoMapper.selectList(wrapper);
        if(CollectionUtil.isEmpty(arSourceInfoList)){
            return;
        }
        for(ArSourceInfo arSourceInfo : arSourceInfoList){
            String loadPath = arSourceInfo.getModelOldUrl();
            if(StringUtils.isNotBlank(loadPath)){
                String path = zipToModel(loadPath);
                int threeModelStatus = 0;
                if(StringUtils.isNotBlank(path)){
                    threeModelStatus = 1;
                    arSourceInfo.setModelUrl(path);
                }else{
                    threeModelStatus = 2;
                }
                arSourceInfo.setModelStatus(threeModelStatus);
                this.arSourceInfoMapper.updateById(arSourceInfo);
            }
        }
    }

    public String zipToModel(String zipPath){
        String returnStr = "";
        if(StringUtils.isBlank(zipPath)){
            return returnStr;
        }
        zipPath = tempFtpPath +  File.separator + zipPath;
        String resourcePath = zipPath.substring(0,zipPath.lastIndexOf("."));
        unzip(zipPath,resourcePath);
        filesPath = new LinkedList<>();
        getFilesPath(resourcePath);
        String objPath = "";
        for(int i = 0 ;i < filesPath.size();i++){
            if(filesPath.get(i).getPath().endsWith(".obj") || filesPath.get(i).getPath().endsWith(".OBJ")){
                objPath = filesPath.get(i).getPath();
            };
        }
        List<String> params = new ArrayList<>();
        if (System.getProperty("os.name").toLowerCase().startsWith("win")) {
            params.add("cmd");
            params.add("/c");
        }
        params.add("obj2gltf");
        params.add("-i");
        params.add(objPath.replace("/", File.separator));
        params.add("-o");
        String outPath = resourcePath+"/gltf";
        params.add((outPath+"/model.gltf").replace("/",File.separator));
        params.add("-s");
        log.info("-------params:{}", String.join(" ", params));
        CommandUtil.command(params);
        if(!new File(outPath+"/model.gltf").exists()){
            log.error("转码失败，未输出GLTF文件！");
            return returnStr;
        }
        String modelJson = StrUtil.format(MODEL_JSON_CONTENT, "model.gltf", "model.gltf");
        InputStream saveinputStream = IOUtils.toInputStream(modelJson, Charsets.toCharset("UTF-8"));
        saveFileFromInputStream(saveinputStream, outPath + File.separator + "model.json");

        //outPath文件下所有文件上传到minio
        String minioPath = uploadModelToMinio(outPath);
        //删除模型压缩包
        File zipFile = new File(zipPath);
        if(zipFile.exists()){
            zipFile.delete();
        }
        // 删除本地解压后的模型包
        File modelFile = new File(resourcePath);
        if(modelFile.exists()){
            modelFile.delete();
        }
        returnStr = minioPath + MinioUtils.SEPARATOR +"model.json";
        return returnStr;
    }

    /**
     * 将三维模型文件上传到minio
     * @param outPath  三维模型地址
     */
    private String uploadModelToMinio(String outPath) {
        String currentDate = DateUtils.getDate();
        String currentPath = UUIDUtils.generateUUID();
        try {
            File file = new File(outPath);
            File[] files = file.listFiles();
            // 逐个文件上传
            for (File file2 : files) {
                String baseUrlStr = currentDate + MinioUtils.SEPARATOR + currentPath + MinioUtils.SEPARATOR + "gltf";
                if (file2.isFile()) {
                    baseUrlStr = baseUrlStr + MinioUtils.SEPARATOR + file2.getName();
                    InputStream inputStream = Files.newInputStream(file2.toPath());
                    MinioUtils.putObject(bucketName, baseUrlStr, inputStream);
                }
            }
        }catch (Exception e){
            log.error("将模型文件上传到minio失败！",e);
        }
        return  currentDate + MinioUtils.SEPARATOR + currentPath + MinioUtils.SEPARATOR + "gltf";
    }


    private void getFilesPath(String dir) {
        File file = new File(dir);
        if(file.exists()){
            if(file.isDirectory()){
                File[] listFiles = file.listFiles();
                for(int i = 0 ; i < listFiles.length ; i++ ){
                    getFilesPath(listFiles[i].getAbsolutePath());
                }
            }else{
                filesPath.add(file);
            }
        }
    }
    public void unzip(String zipPath,String resourcePath){
        File pathFile = new File(resourcePath);
        if(!pathFile.exists()){
            pathFile.mkdirs();
        }
        ZipFile zp;
        try{
            zp=new ZipFile(zipPath, Charset.forName("gbk"));
            Enumeration<? extends ZipEntry> entries = zp.entries();
            while(entries.hasMoreElements()){
                ZipEntry entry =  entries.nextElement();
                String zipEntryName = entry.getName();
                InputStream in = zp.getInputStream(entry);
                String outPath = (resourcePath+"/"+zipEntryName).replace("/",File.separator);
                File file = new  File(outPath.substring(0,outPath.lastIndexOf(File.separator)));
                if(!file.exists()){
                    file.mkdirs();
                }
                if(new File(outPath).isDirectory())
                    continue;
                OutputStream out = Files.newOutputStream(Paths.get(outPath));
                byte[] bf=new byte[2048];
                int len;
                while ((len=in.read(bf))>0){
                    out.write(bf,0,len);
                }
                in.close();
                out.close();
            }
            zp.close();
        }catch ( Exception e){
            log.error("解压失败！",e);
        }
    }

    public void saveFileFromInputStream(InputStream inputStream, String filePath) {
        try {
            OutputStream outputStream = Files.newOutputStream(new File(filePath).toPath());
            int bytesRead;
            byte[] buffer = new byte[8192];
            while ((bytesRead = inputStream.read(buffer, 0, 8192)) != -1) {
                outputStream.write(buffer, 0, bytesRead);
            }
            outputStream.close();
            inputStream.close();
            System.out.println("File saved successfully to: " + filePath);
        } catch (IOException e) {
            log.error("saveFileFromInputStream error",e);
        }
    }

}
