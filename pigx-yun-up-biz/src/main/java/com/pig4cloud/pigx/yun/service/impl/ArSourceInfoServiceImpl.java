package com.pig4cloud.pigx.yun.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import com.pig4cloud.pigx.yun.entity.ArSourceInfo;
import com.pig4cloud.pigx.yun.mapper.ArSourceInfoMapper;
import com.pig4cloud.pigx.yun.service.ArSourceInfoService;
import com.pig4cloud.pigx.yun.util.AdvancedGeneral;
import com.pig4cloud.pigx.yun.util.FileUtil;
import com.pig4cloud.pigx.yun.util.MinioUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.io.InputStream;
import java.util.Base64;
import java.util.Objects;

/**
* <AUTHOR>
* @description 针对表【ar_source_info(文物资源基础信息表)】的数据库操作Service实现
* @createDate 2025-05-07 11:21:57
*/
@Service
public class ArSourceInfoServiceImpl extends ServiceImpl<ArSourceInfoMapper, ArSourceInfo> implements ArSourceInfoService {

    @Override
    public void addSourceInfo(ArSourceInfo arSourceInfo) throws Exception {
        if(StringUtils.isBlank(arSourceInfo.getPictureUrl())){
            throw new RuntimeException("封面图片不能为空！");
        }
        String pictureName = arSourceInfo.getPictureUrl().substring(arSourceInfo.getPictureUrl().lastIndexOf("/")+1);

        // 上传封面图片到百度智能云
        InputStream inputStream = MinioUtils.getObject(MinioUtils.getBucketName(), arSourceInfo.getPictureUrl());
        String result = AdvancedGeneral.sameHqAdd(convertToBase64(inputStream), pictureName,pictureName);
        if(StringUtils.isBlank(result)){
            throw new RuntimeException("上传图片到百度智能云失败！");
        }
        JSONObject jsonObject = JSONObject.parseObject(result);
        if(!jsonObject.containsKey("cont_sign") || StringUtils.isBlank(jsonObject.getString("cont_sign"))){
            throw new RuntimeException("上传图片到百度智能云失败！");
        }
        String contSign = jsonObject.getString("cont_sign");
        // 判断系统中是否已上传过相同的封面图片
        QueryWrapper<ArSourceInfo> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("picture_name",contSign);
        ArSourceInfo oldArSourceInfo = this.baseMapper.selectOne(queryWrapper);
        if(Objects.nonNull(oldArSourceInfo)){
            throw new RuntimeException("封面图片用于识别文物，不能与其他文物封面图片重复！");
        }
        arSourceInfo.setPictureName(jsonObject.getString("cont_sign"));
        this.baseMapper.insert(arSourceInfo);
    }

    @Override
    public void updateSourceInfo(ArSourceInfo arSourceInfo) throws Exception {
        if(StringUtils.isBlank(arSourceInfo.getPictureUrl())){
            throw new RuntimeException("封面图片不能为空！");
        }
        ArSourceInfo oldArSourceInfo = this.baseMapper.selectById(arSourceInfo.getId());
        if(Objects.isNull(oldArSourceInfo)){
            return;
        }
        if(!arSourceInfo.getPictureUrl().equals(oldArSourceInfo.getPictureUrl())){
            String pictureName = arSourceInfo.getPictureUrl().substring(arSourceInfo.getPictureUrl().lastIndexOf("/")+1);
            // 上传封面图片到百度智能云
            InputStream inputStream = MinioUtils.getObject(MinioUtils.getBucketName(), arSourceInfo.getPictureUrl());
            String result = AdvancedGeneral.sameHqAdd(convertToBase64(inputStream), pictureName,pictureName);
            if(StringUtils.isBlank(result)){
                throw new RuntimeException("上传图片到百度智能云失败！");
            }
            JSONObject jsonObject = JSONObject.parseObject(result);
            if(!jsonObject.containsKey("cont_sign") || StringUtils.isBlank(jsonObject.getString("cont_sign"))){
                throw new RuntimeException("上传图片到百度智能云失败！");
            }
            String contSign = jsonObject.getString("cont_sign");
            // 判断系统中是否已上传过相同的封面图片
            QueryWrapper<ArSourceInfo> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("picture_name",contSign);
            ArSourceInfo oldInfo = this.baseMapper.selectOne(queryWrapper);
            if(Objects.nonNull(oldInfo)){
                throw new RuntimeException("封面图片用于识别文物，不能与其他文物封面图片重复！");
            }

            arSourceInfo.setPictureName(jsonObject.getString("cont_sign"));
            // 删除百度智能云之前的图片
            AdvancedGeneral.deleteSameSearch(oldArSourceInfo.getPictureName());
        }
        if(!arSourceInfo.getModelOldUrl().equals(oldArSourceInfo.getModelOldUrl())){
            // 删除旧的模型
            String oldModel = oldArSourceInfo.getModelOldUrl();
            if(StringUtils.isNotBlank(oldModel)){
                if(oldModel.endsWith("model.json")){
                    FileUtil.deleteFilePath(oldModel.replace("model.json",""));
                    // 删除minio上的三维模型
                    String minioFilePath = "";
                    try {
                        minioFilePath = oldModel.split("/gltf")[0];
                        MinioUtils.deleteObject(MinioUtils.getBucketName(), minioFilePath);
                    }catch (Exception e){
                        log.error("删除minio上的三维模型失败，minioFilePath：" + minioFilePath);
                    }
                }
            }
            // 更新模型状态
            arSourceInfo.setModelStatus(0);
            arSourceInfo.setModelUrl(null);
        }
        this.baseMapper.updateById(arSourceInfo);
    }

    @Override
    public void deleteSourceInfo(Long id){
        ArSourceInfo arSourceInfo = this.baseMapper.selectById(id);
        if(Objects.isNull(arSourceInfo)){
            return;
        }
        String pictureName = arSourceInfo.getPictureName();
        this.baseMapper.deleteById(id);
        // 删除百度智能云图库对应图片
        AdvancedGeneral.deleteSameSearch(pictureName);
    }

    public static String convertToBase64(InputStream inputStream) throws Exception {
        byte[] bytes = IOUtils.toByteArray(inputStream);
        return Base64.getEncoder().encodeToString(bytes);
    }

}




