package com.pig4cloud.pigx.yun.service.impl;

import com.pig4cloud.pigx.yun.entity.ArSourceInfo;
import com.pig4cloud.pigx.yun.mapper.ArSourceInfoMapper;
import com.pig4cloud.pigx.yun.service.Model3DService;
import com.pig4cloud.pigx.yun.util.FileUtil;
import com.pig4cloud.pigx.yun.util.MinioUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.File;
import java.util.Objects;

@Slf4j
@Service
public class Model3DServiceImpl implements Model3DService {

    @Value("${minio.bucketName}")
    private String bucketName;

    @Resource
    private ArSourceInfoMapper arSourceInfoMapper;

    @Override
    public void deleteModel(Long id,String ftpPath){
        ArSourceInfo arSourceInfo =  this.arSourceInfoMapper.selectById(id);
        if(Objects.isNull(arSourceInfo)){
            throw new RuntimeException("文物信息不存在！");
        }
        String modePath = arSourceInfo.getModelOldUrl();
        String modelUrl = arSourceInfo.getModelUrl();
        if(StringUtils.isNotBlank(modePath)){
           if(modePath.endsWith(".zip")){
               File file = new File(modePath);
               file.deleteOnExit();
           }
           if(modelUrl.endsWith("model.get3d")){
               FileUtil.deleteFilePath(modelUrl.replace("model.get3d",""));
               // 删除minio上的三维模型
               String minioFilePath = "";
               try {
                   minioFilePath = modelUrl.split(MinioUtils.SEPARATOR + bucketName + MinioUtils.SEPARATOR)[1].split("/gltf")[0];
                   MinioUtils.deleteObject(bucketName, minioFilePath);
               }catch (Exception e){
                   log.error("删除minio上的三维模型失败，minioFilePath：" + minioFilePath);
               }
           }
        }
        arSourceInfo.setModelOldUrl("");
        arSourceInfo.setModelUrl("");
        arSourceInfo.setModelStatus(null);
        this.arSourceInfoMapper.updateById(arSourceInfo);
    }
}
