package com.pig4cloud.pigx.yun.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;
import lombok.Data;

/**
 * 文物资源基础信息表
 * @TableName collection_source_info
 */
@TableName(value ="collection_source_info")
@Data
public class CollectionSourceInfo {
    /**
     * 自增id
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 文物名称
     */
    private String culturalName;

    /**
     * 年代
     */
    private String age;

    /**
     * 质地
     */
    private String texture;

    /**
     * 藏品类别
     */
    private String type;

    /**
     * 级别
     */
    private String gradeEvaluationId;

    /**
     * 保存状态
     */
    private String saveState;

    /**
     * 具体尺寸
     */
    private String sizeNote;

    /**
     * 文物介绍
     */
    private String content;

    /**
     * 形式特征
     */
    private String feature;

    /**
     * 文物图片URL,多个用英文逗号隔开
     */
    private String photoUrl;

    /**
     * 3D模型转换状态 0：转换中，1：转换成功 2：转换失败
     */
    private String modelStatus;

    /**
     * 3D模型zip包URL
     */
    private String modelOldUrl;

    /**
     * 3D模型转换后的URL
     */
    private String modelUrl;

    /**
     * 音频地址
     */
    private String audioUrl;

    /**
     * 视频地址
     */
    private String videoUrl;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 修改时间
     */
    private Date gmtModified;
}