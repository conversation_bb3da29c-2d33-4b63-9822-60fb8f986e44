package com.pig4cloud.pigx.yun.util;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import javax.servlet.ServletContext;
import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.List;
import java.util.Map;

/**
 * Description 获取百度智能云token工具类
 *
 * @since 2020/5/27
 */
@Slf4j
public class AccessTokenUtil {
    private static final String apiKey = "ZrmAobYi7r66v6vjgChGyPN1";
    private static final String secretKey = "wwzcBOdimpmtL0FFZ1Kf7r87Ys01KJKX";

    public static String initAndSetAccessToken() {
        // 获取token地址
        String authHost = "https://aip.baidubce.com/oauth/2.0/token?";
        String getAccessTokenUrl = authHost
                // 1. grant_type为固定参数
                + "grant_type=client_credentials"
                // 2. 官网获取的 API Key
                + "&client_id=" + apiKey
                // 3. 官网获取的 Secret Key
                + "&client_secret=" + secretKey;
        try {
            URL realUrl = new URL(getAccessTokenUrl);
            // 打开和URL之间的连接
            HttpURLConnection connection = (HttpURLConnection) realUrl.openConnection();
            connection.setRequestMethod("GET");
            connection.connect();
            // 获取所有响应头字段
            Map<String, List<String>> map = connection.getHeaderFields();
            // 遍历所有的响应头字段

            // 定义 BufferedReader输入流来读取URL的响应
            BufferedReader in = new BufferedReader(new InputStreamReader(connection.getInputStream()));
            String result = "";
            String line;
            while ((line = in.readLine()) != null) {
                result += line;
            }
            JSONObject jsonObject = JSONObject.parseObject (result);
            String access_token = jsonObject.getString("access_token");
            if (null != access_token) {
                ServletContext sc = ServletContextUtil.get();
                sc.removeAttribute("manager_user");
                sc.setAttribute("access_token", access_token);
            }
            return access_token;
        } catch (Exception e) {
            log.error("获取token失败", e);
        }
        return null;
    }

    public static String getAccessToken() {
        String accessToken = (String) ServletContextUtil.get().getAttribute("access_token");
        if (StringUtils.isBlank(accessToken)) {
            return initAndSetAccessToken();
        }
        return accessToken;
    }
}
