package com.pig4cloud.pigx.yun;

import com.pig4cloud.pigx.common.feign.annotation.EnablePigxFeignClients;
import com.pig4cloud.pigx.common.security.annotation.EnablePigxResourceServer;
import com.pig4cloud.pigx.common.swagger.annotation.EnableOpenApi;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.scheduling.annotation.EnableScheduling;

@EnableOpenApi("yunUp")
@EnablePigxFeignClients
@EnablePigxResourceServer
@EnableDiscoveryClient
@EnableScheduling
@SpringBootApplication
public class PigxYunUpBizApplication {

	public static void main(String[] args) {
		SpringApplication.run(PigxYunUpBizApplication.class, args);
	}

}
