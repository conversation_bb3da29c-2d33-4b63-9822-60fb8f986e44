package com.pig4cloud.pigx.yun.util;

import org.springframework.context.annotation.Configuration;

import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

@Configuration
public class ThreadPool {
    //核心线程数
    private final int corePoolSize = 4;

    //最大线程数
    private final int maximumPoolSize = 8;

    //非核心回收超时时间
    private final long keepAliveTime = 5;

    //任务队列
    private final int queue = 1000;

    public ThreadPoolExecutor executor() {
        ThreadPoolExecutor executor = new ThreadPoolExecutor(
                corePoolSize,
                maximumPoolSize,
                keepAliveTime,
                TimeUnit.SECONDS,//超时时间单位
                new ArrayBlockingQueue<>(queue)
        );
        return executor;
    }

}
