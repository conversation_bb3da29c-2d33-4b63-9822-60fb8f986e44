package com.pig4cloud.pigx.yun.util;

import lombok.extern.slf4j.Slf4j;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.util.List;

@Slf4j
public class CommandUtil {
    public static int command(List<String> params) {
        if (params == null || params.isEmpty()) {
            return -1;
        }
        int paramSize = params.size();
        String[] cmd = params.toArray(new String[paramSize]);
        ProcessBuilder processBuilder = new ProcessBuilder(cmd);
        processBuilder.redirectErrorStream(true);
        int exitCode = -1;
        try {
            Process process = processBuilder.start();
            BufferedReader inBr = new BufferedReader(new InputStreamReader(process.getInputStream(), "UTF-8"));
            String lineStr;
            while ((lineStr = inBr.readLine()) != null) {
                log.debug("program print information: {}", lineStr);
            }
            inBr.close();
            exitCode = process.waitFor();
        } catch (Exception e) {
            log.error("program process error: {}", e.getMessage(), e);
            return -1;
        }
        return exitCode;
    }
}
